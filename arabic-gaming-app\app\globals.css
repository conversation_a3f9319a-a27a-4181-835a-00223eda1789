@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 26 26 26;
    --foreground: 255 255 255;
    --primary: 139 38 53;
    --accent: 255 76 76;
    --muted: 42 42 42;
    --border: 58 58 58;
  }

  .dark {
    --background: 26 26 26;
    --foreground: 255 255 255;
    --primary: 139 38 53;
    --accent: 255 76 76;
    --muted: 42 42 42;
    --border: 58 58 58;
  }

  * {
    border-color: hsl(var(--border));
  }

  body {
    background-color: #1a1a1a;
    color: #ffffff;
  }

  html {
    background-color: #1a1a1a;
  }
}

/* Custom scrollbar for dark theme */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #2a2a2a;
}

::-webkit-scrollbar-thumb {
  background: #4a4a4a;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #5a5a5a;
}
