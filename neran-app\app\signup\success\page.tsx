'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { CheckCircle, MessageCircle, ArrowRight, Mail, RefreshCw } from 'lucide-react'
import { useSupabaseAuth } from '../../../contexts/SupabaseAuthContext'
import CopyButton from '../../components/CopyButton'
import { toast } from '../../../hooks/use-toast'

export default function SignupSuccessPage() {
  const { user, refreshUserData } = useSupabaseAuth()
  const router = useRouter()
  const [whatsAppNumber, setWhatsAppNumber] = useState<string>("+966501234567")
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [lastChecked, setLastChecked] = useState<Date | null>(null)

  useEffect(() => {
    // Redirect if no user or user is already active
    if (!user) {
      router.push('/signup')
      return
    }
    
    if (user.status === 'active') {
      router.push('/')
      return
    }

    // Fetch WhatsApp number from settings API
    const fetchWhatsAppNumber = async () => {
      try {
        const response = await fetch('/api/settings')
        if (response.ok) {
          const settings = await response.json()
          if (settings && settings.supportWhatsApp) {
            setWhatsAppNumber(settings.supportWhatsApp)
          }
        }
      } catch (error) {
        console.warn('Could not fetch WhatsApp number from settings, using default:', error)
        // Keep the default number if settings fetch fails
      }
    }

    fetchWhatsAppNumber()
  }, [user, router])

  // Periodic status checking for pending users
  useEffect(() => {
    if (!user || user.status !== 'pending') return

    const checkStatus = async () => {
      try {
        await refreshUserData()
        setLastChecked(new Date())
      } catch (error) {
        // Silent error handling in production
        if (process.env.NODE_ENV === 'development') {
          console.warn('Error during periodic status check:', error)
        }
      }
    }

    // Check every 2 minutes for better performance
    const interval = setInterval(checkStatus, 120000) // Changed from 30s to 2 minutes

    return () => {
      clearInterval(interval)
    }
  }, [user?.id, user?.status, refreshUserData])

  const handleRefreshStatus = async () => {
    setIsRefreshing(true)
    try {
      const oldStatus = user?.status
      await refreshUserData()
      setLastChecked(new Date())

      // Show a subtle toast for manual refresh
      if (oldStatus === 'pending') {
        toast({
          title: "🔄 تم تحديث الحالة",
          description: "تم فحص حالة حسابك بنجاح",
          variant: "default",
        })
      }
    } catch (error) {
      console.error('Error refreshing status:', error)
      toast({
        title: "❌ خطأ في التحديث",
        description: "حدث خطأ أثناء فحص حالة الحساب. يرجى المحاولة مرة أخرى.",
        variant: "destructive",
      })
    } finally {
      setIsRefreshing(false)
    }
  }

  const handleWhatsAppContact = () => {
    if (!user) return

    const message = `🔥 *طلب تأكيد التسجيل - نيران كارد*\n\n` +
      `مرحباً، أريد تأكيد تسجيل حسابي الجديد:\n\n` +
      `👤 *معرف المستخدم:* ${user.id}\n` +
      `📧 *الاسم:* ${user.name}\n` +
      `📧 *البريد الإلكتروني:* ${user.email}\n\n` +
      `يرجى تفعيل حسابي لأتمكن من استخدام الموقع.\n\n` +
      `شكراً لكم 🙏`

    const phoneNumber = whatsAppNumber.replace(/\+/g, '')
    const whatsappUrl = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`
    window.open(whatsappUrl, '_blank')
  }

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-900">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-500 mx-auto mb-4"></div>
          <div className="text-white">جاري التحميل...</div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex items-center justify-center px-4 py-8 bg-gray-900">
      <div className="w-full max-w-md">
        {/* Main Success Card */}
        <div className="bg-gray-800 border border-gray-600 rounded-2xl p-6 shadow-2xl">
          {/* Success Header */}
          <div className="text-center mb-6">
            <div className="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <CheckCircle className="w-8 h-8 text-white" />
            </div>
            <h1 className="text-xl font-bold text-white mb-1">تم إنشاء حسابك بنجاح! 🎉</h1>
            <p className="text-gray-400 text-sm">مرحباً بك في نيران كارد</p>
          </div>

          {/* User ID Section */}
          <div className="bg-gray-900 border border-gray-600 rounded-xl p-4 mb-4">
            <div className="flex items-center justify-between">
              <div className="flex-1 min-w-0">
                <label className="block text-xs font-medium text-gray-400 mb-1">معرف المستخدم</label>
                <p className="text-white font-mono text-sm truncate">{user.id}</p>
              </div>
              <CopyButton text={user.id} />
            </div>
          </div>

          {/* User Info */}
          <div className="grid grid-cols-2 gap-3 mb-4">
            <div className="bg-gray-900 border border-gray-600 rounded-lg p-3">
              <label className="block text-xs font-medium text-gray-400 mb-1">الاسم</label>
              <p className="text-white text-sm truncate">{user.name}</p>
            </div>
            <div className="bg-gray-900 border border-gray-600 rounded-lg p-3">
              <label className="block text-xs font-medium text-gray-400 mb-1">البريد</label>
              <p className="text-white text-sm truncate">{user.email}</p>
            </div>
          </div>

          {/* Status Information */}
          <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-xl p-4 mb-4">
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 bg-yellow-600 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <Mail className="w-3 h-3 text-white" />
              </div>
              <div className="flex-1">
                <div className="flex items-center justify-between mb-1">
                  <h3 className="text-yellow-400 font-semibold text-sm">حسابك قيد المراجعة</h3>
                  <button
                    onClick={handleRefreshStatus}
                    disabled={isRefreshing}
                    className="text-yellow-400 hover:text-yellow-300 disabled:opacity-50 disabled:cursor-not-allowed"
                    title="تحديث الحالة"
                  >
                    <RefreshCw className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
                  </button>
                </div>
                <p className="text-yellow-300 text-xs leading-relaxed">
                  تم إرسال رسالة تأكيد لبريدك. سيتم تفعيل حسابك خلال 24-48 ساعة.
                  <br />
                  <span className="text-yellow-400 text-xs">
                    يتم فحص الحالة تلقائياً كل 30 ثانية
                    {lastChecked && (
                      <span className="text-yellow-500">
                        {' • آخر فحص: '}
                        {lastChecked.toLocaleTimeString('en-US', {
                          hour: '2-digit',
                          minute: '2-digit',
                          second: '2-digit',
                          hour12: false
                        })}
                      </span>
                    )}
                  </span>
                </p>
              </div>
            </div>
          </div>

          {/* WhatsApp Contact */}
          <div className="bg-green-500/10 border border-green-500/20 rounded-xl p-4 mb-4">
            <h3 className="text-green-400 font-semibold mb-2 text-sm flex items-center gap-2">
              <MessageCircle className="w-4 h-4" />
              تسريع التفعيل
            </h3>
            <p className="text-green-300 text-xs mb-3 leading-relaxed">
              تواصل معنا عبر واتساب للحصول على تفعيل فوري
            </p>

            <button
              onClick={handleWhatsAppContact}
              className="w-full bg-green-600 hover:bg-green-700 text-white py-3 px-4 rounded-lg transition-colors font-medium flex items-center justify-center gap-2 text-sm"
            >
              <MessageCircle className="w-4 h-4" />
              تأكيد عبر واتساب
            </button>
          </div>

          {/* Quick Steps */}
          <div className="bg-blue-500/10 border border-blue-500/20 rounded-xl p-4 mb-4">
            <h3 className="text-blue-400 font-semibold mb-2 text-sm">الخطوات التالية:</h3>
            <div className="space-y-1">
              <div className="flex items-center gap-2 text-xs text-blue-300">
                <span className="text-blue-400">1.</span>
                <span>تأكيد البريد الإلكتروني</span>
              </div>
              <div className="flex items-center gap-2 text-xs text-blue-300">
                <span className="text-blue-400">2.</span>
                <span>انتظار تفعيل الحساب (24-48 ساعة)</span>
              </div>
              <div className="flex items-center gap-2 text-xs text-blue-300">
                <span className="text-blue-400">3.</span>
                <span>أو التواصل عبر واتساب للتسريع</span>
              </div>
            </div>
          </div>

          {/* Action Button */}
          <div className="text-center">
            <button
              onClick={() => router.push('/')}
              className="w-full bg-red-600 hover:bg-red-700 text-white py-3 px-4 rounded-lg transition-colors font-medium flex items-center justify-center gap-2 text-sm"
            >
              <ArrowRight className="w-4 h-4" />
              العودة للرئيسية
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
