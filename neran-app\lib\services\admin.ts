/**
 * Simplified Admin Service
 * Just re-exports SupabaseAdminService methods for compatibility
 */

import { SupabaseAdminService } from './supabase-admin'

export class AdminService {
  // Re-export all methods from SupabaseAdminService for backward compatibility
  static async getAllProducts() {
    return SupabaseAdminService.getAllProducts()
  }

  static async getSystemSettings() {
    return SupabaseAdminService.getSystemSettings()
  }

  static async getDashboardStats() {
    return SupabaseAdminService.getDashboardStats()
  }

  static async getAllUsers(page: number = 1, limit: number = 50) {
    return SupabaseAdminService.getAllUsers(page, limit)
  }

  static async getOrdersWithDetails(
    status?: 'pending' | 'processing' | 'completed' | 'cancelled',
    page: number = 1,
    limit: number = 5
  ) {
    return SupabaseAdminService.getOrdersWithDetails(status, page, limit)
  }

  static async updateUserStatus(userId: string, status: 'pending' | 'active' | 'suspended') {
    return SupabaseAdminService.updateUserStatus(userId, status)
  }

  static async updateUserBalance(userId: string, newBalance: number, adminId?: string, notes?: string) {
    return SupabaseAdminService.updateUserBalance(userId, newBalance, adminId, notes)
  }

  static async updateOrderStatus(
    orderId: string,
    status: 'pending' | 'processing' | 'completed' | 'cancelled',
    adminId?: string,
    adminNotes?: string
  ) {
    return SupabaseAdminService.updateOrderStatus(orderId, status, adminId, adminNotes)
  }

  static async createProduct(productData: any) {
    return SupabaseAdminService.createProduct(productData)
  }

  static async updateProduct(productId: string, updates: any) {
    return SupabaseAdminService.updateProduct(productId, updates)
  }

  static async deleteProduct(productId: string) {
    return SupabaseAdminService.deleteProduct(productId)
  }

  static async updateSystemSettings(settings: any) {
    return SupabaseAdminService.updateSystemSettings(settings)
  }
} 