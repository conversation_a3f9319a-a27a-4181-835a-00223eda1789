'use client'

import { useState } from 'react'
import { Product, User } from '../../types'
import { SSRProduct } from '../../lib/ssr-helpers'
import {
  isOfferActive,
  getPriceDisplayInfo,
  getEffectivePriceByRole,
  getRoleBasedPrice
} from '../../lib/utils/offers'
import { formatCurrency } from '../../lib/utils/currency'

// Union type to handle both Product and SSRProduct
type ProductType = Product | SSRProduct

interface EnhancedProductCardsProps {
  products: ProductType[]
  selectedProduct: ProductType | null
  setSelectedProduct: (product: ProductType | null) => void
  loading?: boolean
  user?: User | null
}

export default function EnhancedProductCards({
  products,
  selectedProduct,
  setSelectedProduct,
  loading = false,
  user
}: EnhancedProductCardsProps) {
  const [animatingCard, setAnimatingCard] = useState<string | null>(null)
  const [imageErrors, setImageErrors] = useState<Set<string>>(new Set())

  // Get card type background colors for selected state
  const getCardTypeBackground = (type: string) => {
    switch (type) {
      case "gems": return "rgba(147, 51, 234, 0.1)" // purple
      case "membership": return "rgba(59, 130, 246, 0.1)" // blue  
      case "pass": return "rgba(34, 197, 94, 0.1)" // green
      default: return "rgba(239, 68, 68, 0.1)" // red
    }
  }

  // Get card type glow colors
  const getCardTypeGlow = (type: string) => {
    switch (type) {
      case "gems": return "bg-gradient-to-r from-purple-600/20 to-pink-600/20"
      case "membership": return "bg-gradient-to-r from-blue-600/20 to-cyan-600/20"
      case "pass": return "bg-gradient-to-r from-green-600/20 to-emerald-600/20"
      default: return "bg-gradient-to-r from-gray-600/20 to-gray-700/20"
    }
  }

  // Get card type border colors
  const getCardTypeBorder = (type: string, isSelected: boolean) => {
    if (isSelected) {
      switch (type) {
        case "gems": return "border-purple-500 shadow-purple-500/30"
        case "membership": return "border-blue-500 shadow-blue-500/30"
        case "pass": return "border-green-500 shadow-green-500/30"
        default: return "border-red-500 shadow-red-500/30"
      }
    }
    return "border-gray-600/50"
  }

  // Get price text color based on type and selection
  const getPriceColor = (type: string, isSelected: boolean) => {
    if (isSelected) {
      switch (type) {
        case "gems": return "text-purple-400"
        case "membership": return "text-blue-400"
        case "pass": return "text-green-400"
        default: return "text-red-400"
      }
    }
    return "text-red-400"
  }

  // Handle image load errors
  const handleImageError = (productId: string) => {
    setImageErrors(prev => new Set(prev).add(productId))
  }

  // Handle card selection with animation
  const handleCardClick = (product: ProductType) => {
    // Toggle selection - if already selected, deselect it
    if (selectedProduct?.id === product.id) {
      setSelectedProduct(null)
    } else {
      setSelectedProduct(product)
    }

    setAnimatingCard(product.id)

    // Reset animation state after animation completes
    setTimeout(() => {
      setAnimatingCard(null)
    }, 600)
  }

  // Show loading state when products are being fetched
  if (loading || products.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-500 mx-auto mb-4"></div>
        <div className="text-gray-400 text-lg">جاري تحميل المنتجات...</div>
        <div className="text-gray-500 text-sm mt-2">يرجى الانتظار</div>
      </div>
    )
  }

  return (
    <div className="grid grid-cols-2 gap-4 sm:gap-6 overflow-hidden">
      {products.map((product, index) => {
        // Calculate role-based pricing
        const effectivePrice = getEffectivePriceByRole(product, user?.role || 'user')
        const roleBasedPrice = getRoleBasedPrice(product, user?.role || 'user')
        const priceInfo = getPriceDisplayInfo(product)
        const isSelected = selectedProduct?.id === product.id
        const isAnimating = animatingCard === product.id
        const cardTypeBackground = getCardTypeBackground(product.category)
        const cardTypeGlow = getCardTypeGlow(product.category)
        const cardTypeBorder = getCardTypeBorder(product.category, isSelected)
        const priceColor = getPriceColor(product.category, isSelected)
        
        // ONLY use real product images - nothing else
        const hasRealImage = product.imageUrl && !imageErrors.has(product.id)

        return (
          <button
            key={product.id}
            onClick={(e) => {
              e.preventDefault()
              e.stopPropagation()
              handleCardClick(product)
            }}
            className={`
              relative p-4 sm:p-6 rounded-2xl border-2 cursor-pointer overflow-hidden
              transition-all duration-500 ease-out transform
              ${isSelected
                ? `min-h-[240px] sm:min-h-[220px] scale-[1.01] ${cardTypeBorder}`
                : 'min-h-[120px] border-gray-600/50'
              }
              ${isAnimating ? 'animate-card-pulse' : ''}
              hover:scale-[1.01]
            `}
            style={{
              background: isSelected
                ? `linear-gradient(135deg, #2a2a2a, #1f1f1f), ${cardTypeBackground}`
                : 'linear-gradient(135deg, #2a2a2a, #1f1f1f)',
            }}
          >
            {/* Background Image - ONLY REAL IMAGES */}
            {hasRealImage && (
              <div className="absolute inset-0 z-0">
                <img
                  src={product.imageUrl}
                  alt={product.name}
                  className="w-full h-full object-cover"
                  onError={() => handleImageError(product.id)}
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-black/60"></div>
              </div>
            )}

            {/* Popular Badge (Top-Right) */}
            {product.popular && (
              <div className="absolute top-0 right-0 bg-gradient-to-r from-orange-500 to-red-500 text-white text-xs font-bold px-3 py-1 rounded-bl-lg rounded-tr-2xl z-20">
                الأكثر شعبية
              </div>
            )}

            {/* Special Offer Badge (Top-Left) */}
            {user?.role === 'distributor' && product.distributorPrice && product.distributorPrice !== product.price ? (
              // Distributor special pricing badge
              <div className="absolute top-0 left-0 bg-gradient-to-r from-orange-500 to-amber-500 text-white text-xs font-bold px-3 py-1 rounded-br-lg rounded-tl-2xl z-20">
                عرض خاص
              </div>
            ) : user?.role !== 'distributor' && (product.specialOffer || priceInfo.hasActiveOffer) ? (
              // Regular offer badge for non-distributors
              <div className="absolute top-0 left-0 bg-gradient-to-r from-green-500 to-emerald-500 text-white text-xs font-bold px-3 py-1 rounded-br-lg rounded-tl-2xl z-20">
                {priceInfo.hasActiveOffer && priceInfo.discountPercentage 
                  ? `خصم ${priceInfo.discountPercentage}%`
                  : 'عرض خاص'
                }
              </div>
            ) : null}

            {/* Shimmer Effect (Selected Cards Only) */}
            {isSelected && (
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/8 to-transparent animate-shimmer rounded-2xl z-10 pointer-events-none overflow-hidden"></div>
            )}

            {/* Content Area */}
            <div className="relative z-20 flex flex-col items-center justify-center h-full space-y-3 pt-6">
              {/* Title (Always Visible) */}
              <div className={`
                text-lg sm:text-xl font-bold text-white leading-tight text-center px-4 py-2
                transition-all duration-300 transform
                ${isSelected ? '-translate-y-1' : 'translate-y-0'}
                bg-black/60 backdrop-blur-md rounded-lg border border-white/20
                shadow-lg
              `}>
                {product.name}
              </div>

              {/* Progressive Price Reveal */}
              <div className={`
                overflow-hidden transition-all duration-500 ease-out
                ${isSelected ? 'max-h-60 sm:max-h-48 opacity-100' : 'max-h-0 opacity-0'}
              `}>
                <div className="space-y-3">
                  {/* Price Display */}
                  <div className="text-center bg-black/60 backdrop-blur-md rounded-lg border border-white/20 p-4 space-y-3">
                    {/* Main Current Price - Large and Prominent */}
                    <div className={`text-3xl font-bold ${priceColor}`}>
                      {formatCurrency(effectivePrice)}
                    </div>

                    {/* Role-specific pricing information */}
                    {user?.role === 'distributor' && product.distributorPrice && product.distributorPrice !== product.price ? (
                      // Distributor-specific pricing display
                      <div className="space-y-2">
                        <div className="text-xs text-orange-300 font-medium">
                          سعر الموزع
                        </div>
                        
                        {/* Show regular vs distributor price comparison */}
                        <div className="bg-orange-500/20 backdrop-blur-md rounded-lg border border-orange-400/30 p-3 space-y-2">
                          <div className="flex justify-between items-center text-sm">
                            <div className="text-right">
                              <div className="text-gray-300 text-xs">السعر العادي</div>
                              <div className="text-gray-400 line-through font-medium">
                                {formatCurrency(product.price)}
                              </div>
                            </div>
                            <div className="text-center text-gray-400">←</div>
                            <div className="text-left">
                              <div className="text-orange-300 text-xs">سعر الموزع</div>
                              <div className="text-orange-400 font-bold">
                                {formatCurrency(product.distributorPrice)}
                              </div>
                            </div>
                          </div>

                          {/* Distributor Savings Badge */}
                          <div className="flex justify-center">
                            {(() => {
                              const savings = product.price - product.distributorPrice
                              const savingsPercent = Math.round((savings / product.price) * 100)
                              return (
                                <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-bold bg-orange-500 text-white">
                                  {formatCurrency(savings)} وفر {savingsPercent}%
                                </span>
                              )
                            })()}
                          </div>
                        </div>
                      </div>
                    ) : user?.role !== 'distributor' && priceInfo.hasActiveOffer && priceInfo.originalPrice ? (
                      // Regular offer display for non-distributors
                      <div className="space-y-2">
                        <div className="bg-black/40 backdrop-blur-md rounded-lg border border-white/10 p-3 space-y-2">
                          {/* Price Comparison Row */}
                          <div className="flex justify-between items-center text-sm">
                            <div className="text-right">
                              <div className="text-gray-300 text-xs">كان</div>
                              <div className="text-gray-400 line-through font-medium">
                                {formatCurrency(priceInfo.originalPrice)}
                              </div>
                            </div>
                            <div className="text-center text-gray-400">←</div>
                            <div className="text-left">
                              <div className="text-gray-300 text-xs">الآن</div>
                              <div className="text-green-400 font-bold">
                                {formatCurrency(effectivePrice)}
                              </div>
                            </div>
                          </div>

                          {/* Savings Badge */}
                          <div className="flex justify-center">
                            <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-bold bg-green-500 text-white">
                              {formatCurrency(priceInfo.savingsAmount)} وفر {priceInfo.discountPercentage}%
                            </span>
                          </div>
                        </div>
                      </div>
                    ) : (
                      // Simple price display for regular users without offers
                      <div className="text-xs text-gray-400">
                        السعر الحالي
                      </div>
                    )}
                  </div>

                  {/* Additional Product Info */}
                  {product.description && (
                    <div className="text-sm text-gray-300 text-center bg-black/40 backdrop-blur-md rounded-lg p-2">
                      {product.description}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </button>
        )
      })}
    </div>
  )
}