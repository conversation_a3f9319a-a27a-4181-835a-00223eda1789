"use client"

import React, { useState, useEffect, useCallback } from 'react'
import { Users, UserCheck, Ban, Search, Filter, RefreshCw, ChevronDown, 
         Check, X, DollarSign, MoreVertical, UserPlus, AlertTriangle, Copy } from 'lucide-react'
import { User } from "../../../types"
import { toast } from "../../../hooks/use-toast"
import { useSupabaseAuth } from "../../../contexts/SupabaseAuthContext"
import { formatDateTime } from "../../../lib/utils/date-utils"
import { useAdminUsersPaginated } from "../../../hooks/useAdminAuth"

// Types
type UserFilter = 'all' | 'pending' | 'active' | 'suspended'
type ActionType = 'approve' | 'ban' | 'unban' | 'makeAdmin' | 'makeUser' | 'makeDistributor' | 'updateBalance' | 'delete'

// Format date for display using centralized utility
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('ar-SA', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

export default function UserManagement() {
  const { user: currentUser } = useSupabaseAuth()
  const [searchTerm, setSearchTerm] = useState('')
  const [activeSearchTerm, setActiveSearchTerm] = useState('') // Only this triggers API calls
  const [filter, setFilter] = useState<UserFilter>('all')
  const [currentPage, setCurrentPage] = useState(1)
  const [showMobileFilters, setShowMobileFilters] = useState(false)
  const [actionLoading, setActionLoading] = useState<string | null>(null)
  const [confirmAction, setConfirmAction] = useState<{userId: string, action: ActionType, userName: string} | null>(null)
  const [editingBalance, setEditingBalance] = useState<string | null>(null)
  const [newBalance, setNewBalance] = useState("")
  const [selectedUsers, setSelectedUsers] = useState<Set<string>>(new Set())
  const [openDropdown, setOpenDropdown] = useState<string | null>(null)
  const [optimisticUpdates, setOptimisticUpdates] = useState<Record<string, Partial<User>>>({})

  // ✅ PERFORMANCE: Use paginated hook with server-side filtering - Manual search trigger
  const {
    data: usersData,
    isLoading: loading,
    error: fetchError,
    refetch
  } = useAdminUsersPaginated(currentPage, 20, activeSearchTerm, filter)

  const users = usersData?.users || []
  const totalPages = usersData?.totalPages || 0
  const totalCount = usersData?.totalCount || 0
  const error = fetchError ? 'فشل في تحميل المستخدمين' : ''

  // ✅ PERFORMANCE: Server-side filtering provides status counts
  const statusCounts = {
    all: totalCount,
    pending: 0, // Would need separate API call for exact counts
    active: 0,  // For now, show total count for 'all'
    suspended: 0,
  }

  // ✅ NEW: Copy to clipboard functionality
  const copyToClipboard = async (text: string, label: string) => {
    try {
      await navigator.clipboard.writeText(text)
      toast({
        title: "تم النسخ",
        description: `تم نسخ ${label} بنجاح`,
        variant: "default",
      })
    } catch (err) {
      console.error('Failed to copy:', err)
      // Fallback for older browsers
      const textArea = document.createElement('textarea')
      textArea.value = text
      document.body.appendChild(textArea)
      textArea.select()
      try {
        document.execCommand('copy')
        toast({
          title: "تم النسخ",
          description: `تم نسخ ${label} بنجاح`,
          variant: "default",
        })
      } catch (fallbackErr) {
        toast({
          title: "خطأ",
          description: "فشل في نسخ النص",
          variant: "destructive",
        })
      }
      document.body.removeChild(textArea)
    }
  }

  // Notification function for role changes
  const notifyUserOfRoleChange = async (userId: string, newRole: string) => {
    try {
      await fetch('/api/notifications', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId,
          type: 'role_change',
          title: 'تم تغيير صلاحياتك',
          message: `تم تغيير دورك إلى: ${newRole === 'admin' ? 'مدير' : newRole === 'distributor' ? 'موزع' : 'مستخدم'}`,
          priority: 'high'
        })
      })
    } catch (error) {
      console.log('Could not send notification:', error)
    }
  }

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (openDropdown) {
        const target = event.target as Element
        if (!target.closest('.relative')) {
          setOpenDropdown(null)
        }
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [openDropdown])

  // Reset to page 1 when search or filter changes
  useEffect(() => {
    setCurrentPage(1)
  }, [activeSearchTerm, filter])

  // ✅ SEARCH FUNCTIONS: Manual search trigger (like OrderManagement)
  const handleSearch = () => {
    const trimmedSearch = searchTerm.trim()
    console.log(`🔍 Performing user search for: "${trimmedSearch}"`)
    setActiveSearchTerm(trimmedSearch)
    setCurrentPage(1)
  }

  const handleSearchKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch()
    }
  }

  const clearSearch = () => {
    setSearchTerm("")
    setActiveSearchTerm("")
    setCurrentPage(1)
  }

  // Manual refresh function
  const handleRefresh = () => {
    refetch()
  }

  // Handle user actions
  const handleUserAction = async (userId: string, action: ActionType) => {
    try {
      setActionLoading(userId)

      // Check if trying to perform restricted action on current user
      if (currentUser && userId === currentUser.id) {
        if (action === 'ban' || action === 'makeUser' || action === 'delete') {
          toast({
            title: "غير مسموح",
            description: "لا يمكنك تنفيذ هذا الإجراء على حسابك الحالي",
            variant: "destructive",
          })
          return
        }
      }

      // Optimistic update - immediately show the expected change
      const optimisticUpdate: Partial<User> = {}
      if (action === 'approve') {
        optimisticUpdate.status = 'active'
      } else if (action === 'ban') {
        optimisticUpdate.status = 'suspended'
      } else if (action === 'unban') {
        optimisticUpdate.status = 'active'
      } else if (action === 'makeAdmin') {
        optimisticUpdate.role = 'admin'
      } else if (action === 'makeUser') {
        optimisticUpdate.role = 'user'
      } else if (action === 'makeDistributor') {
        optimisticUpdate.role = 'distributor'
      } else if (action === 'updateBalance') {
        optimisticUpdate.balance = parseFloat(newBalance) || 0
      }

      setOptimisticUpdates(prev => ({ ...prev, [userId]: optimisticUpdate }))
      console.log(`🔧 UserManagement: Updating user ${userId} action ${action}`)
      
      let endpoint = '/api/admin/users'
      let body: any = { userId, action }

      if (action === 'updateBalance') {
        body.data = { balance: parseFloat(newBalance) || 0 }
      }

      const response = await fetch(endpoint, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(body)
      })

      const result = await response.json()
      
      if (!response.ok) {
        throw new Error(result.error || 'فشل في تنفيذ العملية')
      }
      
      if (result.error) {
        throw new Error(result.error)
      }

      console.log('✅ UserManagement: User action completed successfully')

      // Since we have no cache, just trigger a simple refetch
      console.log('🔄 UserManagement: Triggering fresh data fetch (no cache)...')
      
      // Small delay to ensure server has processed the change
      await new Promise(resolve => setTimeout(resolve, 300))
      
      // Trigger refetch - since cache is disabled, this will always fetch fresh data
      await refetch()
      
      console.log('✅ UserManagement: Fresh data fetched successfully')

      // Notify user if role changed
      if (action === 'makeAdmin') {
        await notifyUserOfRoleChange(userId, 'admin')
        toast({
          title: "تم بنجاح",
          description: "تم ترقية المستخدم إلى مدير",
          variant: "default",
        })
      } else if (action === 'makeUser') {
        await notifyUserOfRoleChange(userId, 'user')
        toast({
          title: "تم بنجاح", 
          description: "تم تحويل المدير إلى مستخدم عادي",
          variant: "default",
        })
      } else if (action === 'makeDistributor') {
        await notifyUserOfRoleChange(userId, 'distributor')
        toast({
          title: "تم بنجاح",
          description: "تم تحويل المستخدم إلى موزع",
          variant: "default",
        })
      } else if (action === 'approve') {
        toast({
          title: "تم بنجاح",
          description: "تم قبول المستخدم وتفعيل حسابه",
          variant: "default",
        })
      } else if (action === 'ban') {
        toast({
          title: "تم بنجاح",
          description: "تم حظر المستخدم",
          variant: "default",
        })
      } else if (action === 'unban') {
        toast({
          title: "تم بنجاح",
          description: "تم إلغاء حظر المستخدم",
          variant: "default",
        })
      } else if (action === 'updateBalance') {
        toast({
          title: "تم بنجاح",
          description: "تم تحديث رصيد المستخدم",
          variant: "default",
        })
      }

      // Reset states
      if (action === 'updateBalance') {
        setEditingBalance(null)
        setNewBalance("")
      }
      setConfirmAction(null)

    } catch (err) {
      console.error('Error handling user action:', err)
      const errorMessage = err instanceof Error ? err.message : 'فشل في تنفيذ العملية'
      toast({
        title: "خطأ",
        description: errorMessage,
        variant: "destructive",
      })
    } finally {
      setActionLoading(null)
      // Clear optimistic update after processing
      setOptimisticUpdates(prev => {
        const newState = { ...prev }
        delete newState[userId]
        return newState
      })
    }
  }

  // Delete user
  const deleteUser = async (userId: string) => {
    try {
      setActionLoading(userId)

      // Check if trying to delete current user
      if (currentUser && userId === currentUser.id) {
        toast({
          title: "غير مسموح",
          description: "لا يمكنك حذف حسابك الحالي",
          variant: "destructive",
        })
        return
      }
      
      const response = await fetch(`/api/admin/users?userId=${userId}`, {
        method: 'DELETE'
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'فشل في حذف المستخدم')
      }

      // Refresh data after successful deletion
      await refetch()
      setConfirmAction(null)

      toast({
        title: "تم بنجاح",
        description: "تم حذف المستخدم بنجاح",
        variant: "default",
      })

    } catch (err) {
      console.error('Error deleting user:', err)
      const errorMessage = err instanceof Error ? err.message : 'فشل في حذف المستخدم'
      toast({
        title: "خطأ",
        description: errorMessage,
        variant: "destructive",
      })
    } finally {
      setActionLoading(null)
    }
  }

  // Get effective user values (optimistic update or actual values)
  const getEffectiveUser = (user: User): User => {
    const optimisticUpdate = optimisticUpdates[user.id]
    if (!optimisticUpdate) return user
    
    return {
      ...user,
      ...optimisticUpdate
    }
  }

  // Reset filters
  const resetFilters = useCallback(() => {
    setSearchTerm("")
    setActiveSearchTerm("")
    setFilter('all')
    setCurrentPage(1)
    setSelectedUsers(new Set())
  }, [])

  if (loading) {
    return (
      <div className="bg-gray-800 border border-gray-600 rounded-xl overflow-hidden">
        {/* Header Skeleton */}
        <div className="p-4 border-b border-gray-600">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="h-6 bg-gray-700 rounded w-48 animate-pulse"></div>
            <div className="h-10 bg-gray-700 rounded w-full sm:w-64 animate-pulse"></div>
          </div>
        </div>

        {/* Filter Skeleton */}
        <div className="p-4 border-b border-gray-600">
          <div className="flex gap-2 mb-4">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="h-8 bg-gray-700 rounded w-20 animate-pulse"></div>
            ))}
          </div>
          <div className="flex gap-2">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="h-6 bg-gray-700 rounded w-16 animate-pulse"></div>
            ))}
          </div>
        </div>

        {/* Users Skeleton */}
        <div className="divide-y divide-gray-600">
          {[1, 2, 3, 4, 5].map((i) => (
            <div key={i} className="p-4">
              <div className="flex items-start gap-3">
                <div className="w-10 h-10 bg-gray-700 rounded-full animate-pulse flex-shrink-0"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-gray-700 rounded w-32 animate-pulse"></div>
                  <div className="h-3 bg-gray-700 rounded w-48 animate-pulse"></div>
                  <div className="h-3 bg-gray-700 rounded w-24 animate-pulse"></div>
                </div>
                <div className="flex gap-2">
                  <div className="w-8 h-8 bg-gray-700 rounded-lg animate-pulse"></div>
                  <div className="w-8 h-8 bg-gray-700 rounded-lg animate-pulse"></div>
                  <div className="w-8 h-8 bg-gray-700 rounded-lg animate-pulse"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-gray-800 border border-gray-600 rounded-xl p-6">
        <div className="text-center py-8">
          <div className="text-red-400 mb-4">{error}</div>
          <button
            onClick={() => refetch()}
            disabled={loading}
            className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg disabled:opacity-50"
          >
            {loading ? 'جاري المحاولة...' : 'إعادة المحاولة'}
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Enhanced Mobile-First User Management */}
      <div className="bg-gray-800 border border-gray-600 rounded-xl overflow-hidden">
        {/* Header with Pull-to-Refresh */}
        <div className="p-4 border-b border-gray-600">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="flex items-center gap-3">
              <Users className="w-6 h-6 text-blue-400" />
              <div>
                <h2 className="text-xl font-bold text-white">إدارة المستخدمين</h2>
                <p className="text-sm text-gray-400">
                  {activeSearchTerm ? (
                    <>
                      نتائج البحث: {totalCount} | البحث عن: "{activeSearchTerm}"
                    </>
                  ) : (
                    <>
                      إجمالي: {totalCount} | الصفحة: {currentPage} من {totalPages}
                    </>
                  )}
                </p>
              </div>
            </div>
            
            {/* Enhanced Search with Manual Trigger - Similar to OrderManagement */}
            <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 w-full sm:w-auto">
              <div className="relative flex-1 sm:w-64">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="البحث: الاسم، البريد، الدور (admin/user), الحالة (active/pending)..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onKeyPress={handleSearchKeyPress}
                  className="w-full pl-4 pr-10 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div className="flex items-center gap-2 shrink-0">
                <button
                  onClick={handleSearch}
                  disabled={loading}
                  className="px-4 py-2 bg-blue-600 hover:bg-blue-700 border border-blue-500 rounded-lg text-white text-sm transition-colors disabled:opacity-50 flex items-center gap-2"
                  title="بحث"
                >
                  {loading ? (
                    <>
                      <RefreshCw className="w-3 h-3 animate-spin" />
                      جاري البحث...
                    </>
                  ) : (
                    <>
                      <Search className="w-3 h-3" />
                      بحث
                    </>
                  )}
                </button>
                {activeSearchTerm && (
                  <button
                    onClick={clearSearch}
                    disabled={loading}
                    className="px-3 py-2 bg-gray-700 hover:bg-gray-600 border border-gray-600 rounded-lg text-gray-400 hover:text-white text-sm transition-colors disabled:opacity-50"
                    title="مسح البحث"
                  >
                    مسح
                  </button>
                )}
                {/* Mobile Refresh Button */}
                <button
                  onClick={handleRefresh}
                  disabled={loading}
                  className="sm:hidden bg-gray-700 hover:bg-gray-600 text-white p-2 rounded-lg disabled:opacity-50 transition-colors"
                >
                  <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Mobile Filter Toggle */}
        <div className="sm:hidden p-4 border-b border-gray-600">
          <button
            onClick={() => setShowMobileFilters(!showMobileFilters)}
            className="flex items-center justify-between w-full text-white"
          >
            <span className="flex items-center gap-2">
              <Filter className="w-4 h-4" />
              فلترة وترتيب
            </span>
            <ChevronDown className={`w-4 h-4 transform transition-transform ${showMobileFilters ? 'rotate-180' : ''}`} />
          </button>
        </div>

        {/* Filters */}
        <div className={`${showMobileFilters ? 'block' : 'hidden'} sm:block p-4 border-b border-gray-600 bg-gray-750`}>
          {/* Status Filters */}
          <div className="flex flex-wrap gap-2 mb-4">
            {(['all', 'pending', 'active', 'suspended'] as UserFilter[]).map((status) => (
              <button
                key={status}
                onClick={() => setFilter(status)}
                className={`px-3 py-1.5 rounded-lg text-sm font-medium transition-colors ${
                  filter === status
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                }`}
              >
                {status === 'all' ? 'الكل' : 
                 status === 'pending' ? 'قيد المراجعة' :
                 status === 'active' ? 'نشط' : 'محظور'}
                {status === 'all' && statusCounts.all > 0 && (
                  <span className="ml-1 text-xs bg-gray-600 px-1.5 py-0.5 rounded">
                    {statusCounts.all}
                  </span>
                )}
              </button>
            ))}
          </div>

          {/* Quick Search Buttons */}
          <div className="flex flex-wrap gap-2 mb-3">
            <span className="text-xs text-gray-400 self-center">بحث سريع:</span>
            {['admin', 'user', 'active', 'pending', '@gmail.com'].map((term) => (
              <button
                key={term}
                onClick={() => {
                  setSearchTerm(term)
                  setActiveSearchTerm(term)
                  setCurrentPage(1)
                }}
                className="px-2 py-1 text-xs bg-gray-600 hover:bg-gray-500 text-gray-300 hover:text-white rounded transition-colors"
              >
                {term}
              </button>
            ))}
          </div>

          {/* Action Buttons */}
          <div className="flex flex-wrap gap-2 items-center">
            <button
              onClick={handleRefresh}
              disabled={loading}
              className="hidden sm:flex items-center gap-2 bg-gray-700 hover:bg-gray-600 text-white px-3 py-2 rounded-lg disabled:opacity-50 transition-colors"
            >
              <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
              تحديث
            </button>
            
            <button
              onClick={resetFilters}
              className="flex items-center gap-2 bg-gray-700 hover:bg-gray-600 text-white px-3 py-2 rounded-lg transition-colors"
            >
              <X className="w-4 h-4" />
              إعادة تعيين
            </button>

            {selectedUsers.size > 0 && (
              <div className="flex items-center gap-2 ml-auto">
                <span className="text-sm text-gray-400">
                  تم اختيار {selectedUsers.size} مستخدم
                </span>
                <button
                  onClick={() => setSelectedUsers(new Set())}
                  className="text-gray-400 hover:text-white"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Users List */}
        {users.length === 0 ? (
          <div className="p-8 text-center">
            <Users className="w-12 h-12 text-gray-600 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-400 mb-2">لا توجد مستخدمون</h3>
            <p className="text-gray-500">
              {activeSearchTerm ? (
                <>
                  لا توجد نتائج للبحث عن: "{activeSearchTerm}"
                  <br />
                  <span className="text-xs mt-2 block">
                    جرب البحث عن: الاسم، البريد، admin، user، active، pending
                  </span>
                </>
              ) : filter !== 'all' ? (
                'لا توجد مستخدمون بالحالة المحددة'
              ) : (
                'لم يتم العثور على أي مستخدمين في النظام'
              )}
            </p>
          </div>
        ) : (
          <div className="divide-y divide-gray-600">
            {users.map((user: User) => {
              const effectiveUser = getEffectiveUser(user)
              return (
              <div key={user.id} className={`p-4 hover:bg-gray-750 transition-colors ${actionLoading === user.id ? 'pointer-events-none opacity-70' : ''}`}>
                {actionLoading === user.id && (
                  <div className="absolute inset-0 bg-black/20 rounded-lg flex items-center justify-center z-10">
                    <div className="bg-gray-800/90 px-3 py-2 rounded-lg flex items-center gap-2 text-white text-sm">
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      جاري التحديث...
                    </div>
                  </div>
                )}
                <div className="flex items-start gap-3">
                  {/* User Avatar */}
                  <div className="w-10 h-10 bg-gray-600 rounded-full flex items-center justify-center text-white font-medium text-sm flex-shrink-0">
                    {user.name.slice(0, 2).toUpperCase()}
                  </div>

                  {/* User Info */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <h3 className="font-medium text-white truncate">{user.name}</h3>
                          
                          {/* Role Badge */}
                          <span className={`px-2 py-0.5 rounded-full text-xs font-medium ${
                            effectiveUser.role === 'admin' 
                              ? 'bg-purple-500/20 text-purple-300 border border-purple-500/30'
                              : effectiveUser.role === 'distributor'
                              ? 'bg-orange-500/20 text-orange-300 border border-orange-500/30'
                              : 'bg-blue-500/20 text-blue-300 border border-blue-500/30'
                          }`}>
                            {effectiveUser.role === 'admin' ? 'مدير' : effectiveUser.role === 'distributor' ? 'موزع' : 'مستخدم'}
                          </span>

                          {/* Status Badge */}
                          <span className={`px-2 py-0.5 rounded-full text-xs font-medium ${
                            effectiveUser.status === 'active'
                              ? 'bg-green-500/20 text-green-300 border border-green-500/30'
                              : effectiveUser.status === 'pending'
                              ? 'bg-yellow-500/20 text-yellow-300 border border-yellow-500/30'
                              : 'bg-red-500/20 text-red-300 border border-red-500/30'
                          }`}>
                            {effectiveUser.status === 'active' ? 'نشط' : 
                             effectiveUser.status === 'pending' ? 'قيد المراجعة' : 'محظور'}
                          </span>
                        </div>

                        <p className="text-sm text-gray-400 truncate mb-1">{user.email}</p>
                        
                        <div className="flex items-center gap-4 text-xs text-gray-500">
                          <span>الرصيد: {effectiveUser.balance} ريال</span>
                          <span>انضم: {formatDate(user.createdAt)}</span>
                          {user.gameId && (
                            <div className="flex items-center gap-1">
                              <span>معرف اللعبة: {user.gameId}</span>
                              <button
                                onClick={() => copyToClipboard(user.gameId!, 'معرف اللعبة')}
                                className="p-0.5 hover:bg-gray-600 rounded text-gray-400 hover:text-white transition-colors"
                                title="نسخ معرف اللعبة"
                              >
                                <Copy className="w-3 h-3" />
                              </button>
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Actions */}
                      <div className="flex items-center gap-1 ml-2">
                        {/* Quick Actions */}
                        {user.status === 'pending' && (
                          <button
                            onClick={() => handleUserAction(user.id, 'approve')}
                            disabled={actionLoading === user.id}
                            className="p-1.5 bg-green-600 hover:bg-green-700 text-white rounded-lg disabled:opacity-50 transition-colors"
                            title="قبول المستخدم"
                          >
                            <Check className="w-4 h-4" />
                          </button>
                        )}

                        {user.status === 'active' && (
                          <button
                            onClick={() => setConfirmAction({
                              userId: user.id,
                              action: 'ban',
                              userName: user.name
                            })}
                            disabled={actionLoading === user.id}
                            className="p-1.5 bg-red-600 hover:bg-red-700 text-white rounded-lg disabled:opacity-50 transition-colors"
                            title="حظر المستخدم"
                          >
                            <Ban className="w-4 h-4" />
                          </button>
                        )}

                        {user.status === 'suspended' && (
                          <button
                            onClick={() => handleUserAction(user.id, 'unban')}
                            disabled={actionLoading === user.id}
                            className="p-1.5 bg-green-600 hover:bg-green-700 text-white rounded-lg disabled:opacity-50 transition-colors"
                            title="إلغاء حظر المستخدم"
                          >
                            <UserCheck className="w-4 h-4" />
                          </button>
                        )}

                        {/* Balance Edit */}
                        {editingBalance === user.id ? (
                          <div className="flex items-center gap-1">
                            <input
                              type="number"
                              value={newBalance}
                              onChange={(e) => setNewBalance(e.target.value)}
                              placeholder="الرصيد الجديد"
                              className="w-20 px-2 py-1 bg-gray-700 border border-gray-600 rounded text-white text-sm"
                              onKeyPress={(e) => {
                                if (e.key === 'Enter') {
                                  handleUserAction(user.id, 'updateBalance')
                                }
                              }}
                              autoFocus
                            />
                            <button
                              onClick={() => handleUserAction(user.id, 'updateBalance')}
                              disabled={actionLoading === user.id}
                              className="p-1 bg-green-600 hover:bg-green-700 text-white rounded disabled:opacity-50"
                            >
                              <Check className="w-3 h-3" />
                            </button>
                            <button
                              onClick={() => {
                                setEditingBalance(null)
                                setNewBalance("")
                              }}
                              className="p-1 bg-gray-600 hover:bg-gray-700 text-white rounded"
                            >
                              <X className="w-3 h-3" />
                            </button>
                          </div>
                        ) : (
                          <button
                            onClick={() => {
                              setEditingBalance(user.id)
                              setNewBalance(user.balance.toString())
                            }}
                            disabled={actionLoading === user.id}
                            className="p-1.5 bg-blue-600 hover:bg-blue-700 text-white rounded-lg disabled:opacity-50 transition-colors"
                            title="تعديل الرصيد"
                          >
                            <DollarSign className="w-4 h-4" />
                          </button>
                        )}

                        {/* More Actions Dropdown */}
                        <div className="relative">
                          <button
                            onClick={() => setOpenDropdown(openDropdown === user.id ? null : user.id)}
                            className="p-1.5 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
                          >
                            <MoreVertical className="w-4 h-4" />
                          </button>

                          {openDropdown === user.id && (
                            <div className="absolute left-0 mt-1 w-48 bg-gray-700 border border-gray-600 rounded-lg shadow-xl z-10">
                              <div className="py-1">
                                {user.role === 'user' ? (
                                  <>
                                    <button
                                      onClick={() => {
                                        setConfirmAction({
                                          userId: user.id,
                                          action: 'makeAdmin',
                                          userName: user.name
                                        })
                                        setOpenDropdown(null)
                                      }}
                                      disabled={actionLoading === user.id}
                                      className="w-full text-right px-4 py-2 text-sm text-white hover:bg-gray-600 disabled:opacity-50"
                                    >
                                      <UserPlus className="w-4 h-4 inline ml-2" />
                                      ترقية إلى مدير
                                    </button>
                                    <button
                                      onClick={() => {
                                        setConfirmAction({
                                          userId: user.id,
                                          action: 'makeDistributor',
                                          userName: user.name
                                        })
                                        setOpenDropdown(null)
                                      }}
                                      disabled={actionLoading === user.id}
                                      className="w-full text-right px-4 py-2 text-sm text-white hover:bg-gray-600 disabled:opacity-50"
                                    >
                                      <UserPlus className="w-4 h-4 inline ml-2" />
                                      تحويل إلى موزع
                                    </button>
                                  </>
                                ) : user.role === 'distributor' ? (
                                  <>
                                    <button
                                      onClick={() => {
                                        setConfirmAction({
                                          userId: user.id,
                                          action: 'makeAdmin',
                                          userName: user.name
                                        })
                                        setOpenDropdown(null)
                                      }}
                                      disabled={actionLoading === user.id}
                                      className="w-full text-right px-4 py-2 text-sm text-white hover:bg-gray-600 disabled:opacity-50"
                                    >
                                      <UserPlus className="w-4 h-4 inline ml-2" />
                                      ترقية إلى مدير
                                    </button>
                                    <button
                                      onClick={() => {
                                        setConfirmAction({
                                          userId: user.id,
                                          action: 'makeUser',
                                          userName: user.name
                                        })
                                        setOpenDropdown(null)
                                      }}
                                      disabled={actionLoading === user.id}
                                      className="w-full text-right px-4 py-2 text-sm text-white hover:bg-gray-600 disabled:opacity-50"
                                    >
                                      تحويل إلى مستخدم عادي
                                    </button>
                                  </>
                                ) : (
                                  <>
                                    <button
                                      onClick={() => {
                                        setConfirmAction({
                                          userId: user.id,
                                          action: 'makeUser',
                                          userName: user.name
                                        })
                                        setOpenDropdown(null)
                                      }}
                                      disabled={actionLoading === user.id}
                                      className="w-full text-right px-4 py-2 text-sm text-white hover:bg-gray-600 disabled:opacity-50"
                                    >
                                      تحويل إلى مستخدم عادي
                                    </button>
                                    <button
                                      onClick={() => {
                                        setConfirmAction({
                                          userId: user.id,
                                          action: 'makeDistributor',
                                          userName: user.name
                                        })
                                        setOpenDropdown(null)
                                      }}
                                      disabled={actionLoading === user.id}
                                      className="w-full text-right px-4 py-2 text-sm text-white hover:bg-gray-600 disabled:opacity-50"
                                    >
                                      تحويل إلى موزع
                                    </button>
                                  </>
                                )}

                                <button
                                  onClick={() => {
                                    setConfirmAction({
                                      userId: user.id,
                                      action: 'delete',
                                      userName: user.name
                                    })
                                    setOpenDropdown(null)
                                  }}
                                  disabled={actionLoading === user.id}
                                  className="w-full text-right px-4 py-2 text-sm text-red-400 hover:bg-gray-600 disabled:opacity-50"
                                >
                                  <AlertTriangle className="w-4 h-4 inline ml-2" />
                                  حذف المستخدم
                                </button>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Loading Overlay */}
                {actionLoading === user.id && (
                  <div className="absolute inset-0 bg-gray-800/80 flex items-center justify-center rounded-lg">
                    <div className="flex items-center gap-2 text-white">
                      <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                      جاري المعالجة...
                    </div>
                  </div>
                )}
              </div>
            )})}
          </div>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="p-4 border-t border-gray-600 bg-gray-750">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-400">
                صفحة {currentPage} من {totalPages} (إجمالي: {totalCount})
              </div>
              
              <div className="flex items-center gap-2">
                <button
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  className="px-3 py-1 bg-gray-700 hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg text-sm transition-colors"
                >
                  السابق
                </button>
                
                <div className="flex items-center gap-1">
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    const pageNum = Math.max(1, Math.min(totalPages - 4, currentPage - 2)) + i
                    return (
                      <button
                        key={pageNum}
                        onClick={() => setCurrentPage(pageNum)}
                        className={`w-8 h-8 rounded-lg text-sm font-medium transition-colors ${
                          currentPage === pageNum
                            ? 'bg-blue-600 text-white'
                            : 'bg-gray-700 hover:bg-gray-600 text-gray-300'
                        }`}
                      >
                        {pageNum}
                      </button>
                    )
                  })}
                </div>
                
                <button
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                  className="px-3 py-1 bg-gray-700 hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg text-sm transition-colors"
                >
                  التالي
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Confirmation Modal */}
      {confirmAction && (
        <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4">
          <div className="bg-gray-800 border border-gray-600 rounded-xl p-6 max-w-md w-full">
            <h3 className="text-lg font-bold text-white mb-4">تأكيد العملية</h3>
            
            <p className="text-gray-300 mb-6">
              {confirmAction.action === 'delete' && `هل أنت متأكد من حذف المستخدم "${confirmAction.userName}"؟ هذا الإجراء لا يمكن التراجع عنه.`}
              {confirmAction.action === 'ban' && `هل أنت متأكد من حظر المستخدم "${confirmAction.userName}"؟`}
              {confirmAction.action === 'makeAdmin' && `هل أنت متأكد من ترقية "${confirmAction.userName}" إلى مدير؟`}
              {confirmAction.action === 'makeUser' && `هل أنت متأكد من تحويل "${confirmAction.userName}" إلى مستخدم عادي؟`}
            </p>

            <div className="flex gap-3">
              <button
                onClick={() => {
                  if (confirmAction.action === 'delete') {
                    deleteUser(confirmAction.userId)
                  } else {
                    handleUserAction(confirmAction.userId, confirmAction.action)
                  }
                }}
                disabled={actionLoading === confirmAction.userId}
                className={`flex-1 py-2 px-4 rounded-lg font-medium disabled:opacity-50 transition-colors ${
                  confirmAction.action === 'delete'
                    ? 'bg-red-600 hover:bg-red-700 text-white'
                    : 'bg-blue-600 hover:bg-blue-700 text-white'
                }`}
              >
                {actionLoading === confirmAction.userId ? 'جاري المعالجة...' : 'تأكيد'}
              </button>
              
              <button
                onClick={() => setConfirmAction(null)}
                disabled={actionLoading === confirmAction.userId}
                className="flex-1 py-2 px-4 bg-gray-700 hover:bg-gray-600 text-white rounded-lg font-medium disabled:opacity-50 transition-colors"
              >
                إلغاء
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
