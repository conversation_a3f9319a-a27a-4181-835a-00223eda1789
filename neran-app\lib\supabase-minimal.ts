/**
 * Minimal Supabase Client for Server-Side Operations
 * This client is designed to work without realtime-js dependencies
 */

// Simple HTTP client for Supabase REST API
class MinimalSupabaseClient {
  private baseUrl: string
  private apiKey: string
  private headers: Record<string, string>

  constructor(url: string, key: string) {
    this.baseUrl = url
    this.apiKey = key
    this.headers = {
      'apikey': key,
      'Authorization': `Bearer ${key}`,
      'Content-Type': 'application/json',
      'Prefer': 'return=representation'
    }
  }

  from(table: string) {
    return new MinimalQueryBuilder(this.baseUrl, this.headers, table)
  }
}

class MinimalQueryBuilder {
  private baseUrl: string
  private headers: Record<string, string>
  private table: string
  private selectFields: string = '*'
  private filters: string[] = []
  private orderBy: string = ''
  private limitCount: number | null = null
  private rangeStart: number | null = null
  private rangeEnd: number | null = null

  constructor(baseUrl: string, headers: Record<string, string>, table: string) {
    this.baseUrl = baseUrl
    this.headers = headers
    this.table = table
  }

  select(fields: string = '*') {
    this.selectFields = fields
    return this
  }

  eq(column: string, value: any) {
    this.filters.push(`${column}=eq.${value}`)
    return this
  }

  order(column: string, options: { ascending?: boolean } = {}) {
    const direction = options.ascending === false ? 'desc' : 'asc'
    this.orderBy = `${column}.${direction}`
    return this
  }

  limit(count: number) {
    this.limitCount = count
    return this
  }

  range(start: number, end: number) {
    this.rangeStart = start
    this.rangeEnd = end
    return this
  }

  single() {
    this.limitCount = 1
    return this
  }

  async execute() {
    const url = new URL(`${this.baseUrl}/rest/v1/${this.table}`)
    
    // Add select parameter
    url.searchParams.set('select', this.selectFields)
    
    // Add filters
    this.filters.forEach(filter => {
      const [key, value] = filter.split('=')
      url.searchParams.set(key, value)
    })
    
    // Add order
    if (this.orderBy) {
      url.searchParams.set('order', this.orderBy)
    }
    
    // Add limit
    if (this.limitCount) {
      url.searchParams.set('limit', this.limitCount.toString())
    }
    
    // Add range
    if (this.rangeStart !== null && this.rangeEnd !== null) {
      this.headers['Range'] = `${this.rangeStart}-${this.rangeEnd}`
    }

    try {
      const response = await fetch(url.toString(), {
        method: 'GET',
        headers: this.headers
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      
      // Handle single() case
      if (this.limitCount === 1 && Array.isArray(data)) {
        if (data.length === 0) {
          return { data: null, error: { code: 'PGRST116', message: 'No rows found' } }
        }
        return { data: data[0], error: null }
      }
      
      return { data, error: null }
    } catch (error: any) {
      console.error('Minimal Supabase client error:', error)
      return { data: null, error: { message: error.message } }
    }
  }

  // Alias for execute to match Supabase API
  async then(resolve: any, reject?: any) {
    try {
      const result = await this.execute()
      return resolve(result)
    } catch (error) {
      if (reject) return reject(error)
      throw error
    }
  }
}

// Create minimal admin client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

export const minimalSupabaseAdmin = new MinimalSupabaseClient(supabaseUrl, supabaseServiceKey)

// Fallback function to use minimal client if regular client fails
export async function safeSupabaseQuery<T>(
  regularQuery: () => Promise<{ data: T | null, error: any }>,
  fallbackQuery: () => Promise<{ data: T | null, error: any }>
): Promise<{ data: T | null, error: any }> {
  try {
    return await regularQuery()
  } catch (error) {
    console.warn('Regular Supabase client failed, using minimal client:', error)
    return await fallbackQuery()
  }
}
