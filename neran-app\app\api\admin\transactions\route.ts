import { NextRequest, NextResponse } from 'next/server'
import { TransactionService } from '../../../../lib/services/transactions'

/**
 * Admin Transactions API Route
 * Handles admin-level transaction operations
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '100')

    console.log(`📊 Admin: Fetching all transactions with limit: ${limit}`)

    // Use paginated method to get transactions with the specified limit
    const result = await TransactionService.getAllTransactionsPaginated(1, limit)
    const transactions = result.transactions

    console.log(`✅ Admin: Fetched ${transactions.length} transactions`)

    return NextResponse.json(transactions, {
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
        'Content-Type': 'application/json',
      },
    })
  } catch (error: any) {
    console.error('❌ Admin Transactions API error:', error)
    
    // Return empty array instead of error for graceful handling
    return NextResponse.json([], {
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
        'Content-Type': 'application/json',
      },
    })
  }
}

/**
 * Create admin transactions (deposits, refunds, etc.)
 */
export async function POST(request: NextRequest) {
  try {
    const transactionData = await request.json()

    console.log('📝 Admin: Creating transaction:', transactionData)

    // Validate required fields
    if (!transactionData.type || !transactionData.userId || !transactionData.amount) {
      return NextResponse.json(
        { error: 'Missing required fields: type, userId, amount' },
        { status: 400 }
      )
    }

    let transaction
    switch (transactionData.type) {
      case 'deposit':
        transaction = await TransactionService.adminAddBalance(
          transactionData.userId,
          transactionData.amount,
          transactionData.adminId || 'system',
          transactionData.description
        )
        break
      
      case 'refund':
        transaction = await TransactionService.createOrderRefundTransaction(
          transactionData.userId,
          transactionData.orderId || '',
          transactionData.amount,
          transactionData.description || 'Refund',
          transactionData.adminId,
          transactionData.description
        )
        break
      
      default:
        return NextResponse.json(
          { error: 'Invalid transaction type' },
          { status: 400 }
        )
    }

    console.log('✅ Admin: Transaction created successfully:', transaction.id)

    return NextResponse.json({
      success: true,
      transaction,
      message: 'Transaction created successfully'
    }, {
      status: 201,
      headers: {
        'Content-Type': 'application/json',
      },
    })
  } catch (error: any) {
    console.error('❌ Admin transaction creation error:', error)
    return NextResponse.json(
      { error: error.message || 'Failed to create transaction' },
      { status: 500 }
    )
  }
} 