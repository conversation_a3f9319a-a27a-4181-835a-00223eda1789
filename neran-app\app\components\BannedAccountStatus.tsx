'use client'

import { useState, useEffect } from 'react'
import { Ban, MessageCircle, Co<PERSON>, AlertTriangle } from 'lucide-react'
// Removed Firebase AdminService dependency
import { User as UserType } from '../../types'

interface BannedAccountStatusProps {
  user: UserType
}

export default function BannedAccountStatus({ user }: BannedAccountStatusProps) {
  const [whatsAppNumber, setWhatsAppNumber] = useState<string>("+************")
  const [copied, setCopied] = useState(false)

  useEffect(() => {
    // Fetch WhatsApp number from admin settings
    const fetchWhatsAppNumber = async () => {
      try {
        const response = await fetch('/api/settings')
        if (response.ok) {
          const settings = await response.json()
          if (settings && settings.supportWhatsApp) {
            setWhatsAppNumber(settings.supportWhatsApp)
          }
        }
      } catch (error) {
        // Silent error handling - use default WhatsApp number
      }
    }

    fetchWhatsAppNumber()
  }, [])

  const handleWhatsAppContact = () => {
    const message = `🚫 *استفسار حول حظر الحساب - نيران كارد*\n\n` +
      `مرحباً، أريد الاستفسار عن حظر حسابي:\n\n` +
      `👤 *معرف المستخدم:* ${user.id}\n` +
      `📧 *الاسم:* ${user.name}\n` +
      `📧 *البريد الإلكتروني:* ${user.email}\n\n` +
      `تم حظر حسابي ولا أعرف السبب. يرجى توضيح السبب وإمكانية إعادة التفعيل.\n\n` +
      `شكراً لكم 🙏`

    const phoneNumber = whatsAppNumber.replace(/\+/g, '')

    // Enhanced WhatsApp URL with better iOS support
    const encodedMessage = encodeURIComponent(message)

    // Try multiple WhatsApp URL formats for better compatibility
    const whatsappUrls = [
      `https://wa.me/${phoneNumber}?text=${encodedMessage}`, // Standard
      `https://api.whatsapp.com/send?phone=${phoneNumber}&text=${encodedMessage}`, // Alternative
      `whatsapp://send?phone=${phoneNumber}&text=${encodedMessage}` // App deep link
    ]

    // Try to open WhatsApp with fallback
    const tryOpenWhatsApp = (urlIndex = 0) => {
      if (urlIndex >= whatsappUrls.length) {
        // Fallback: copy message to clipboard
        navigator.clipboard.writeText(message).then(() => {
          alert(`تم نسخ الرسالة. يرجى فتح واتساب والتواصل مع: ${whatsAppNumber}`)
        }).catch(() => {
          alert(`يرجى التواصل مع واتساب: ${whatsAppNumber}`)
        })
        return
      }

      const url = whatsappUrls[urlIndex]
      const newWindow = window.open(url, '_blank')

      // Check if window opened successfully (not blocked)
      setTimeout(() => {
        if (!newWindow || newWindow.closed) {
          tryOpenWhatsApp(urlIndex + 1)
        }
      }, 1000)
    }

    tryOpenWhatsApp()
  }

  const copyUserId = async () => {
    try {
      await navigator.clipboard.writeText(user.id)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.error('Failed to copy:', error)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center px-4 py-8 bg-gray-900">
      <div className="w-full max-w-md">
        {/* Main Card */}
        <div className="bg-gray-800 border border-red-600 rounded-2xl p-6 shadow-2xl">
          {/* Header */}
          <div className="text-center mb-6">
            <div className="w-16 h-16 bg-red-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <Ban className="w-8 h-8 text-white" />
            </div>
            <h1 className="text-xl font-bold text-white mb-1">تم حظر حسابك</h1>
            <p className="text-gray-400 text-sm">مرحباً {user.name}</p>
          </div>

          {/* User ID Section */}
          <div className="bg-gray-900 border border-gray-600 rounded-xl p-4 mb-4">
            <div className="flex items-center justify-between">
              <div className="flex-1 min-w-0">
                <label className="block text-xs font-medium text-gray-400 mb-1">معرف المستخدم</label>
                <p className="text-white font-mono text-sm truncate">{user.id}</p>
              </div>
              <button
                onClick={copyUserId}
                className="mr-3 p-2 text-gray-400 hover:text-white transition-colors"
                title="نسخ المعرف"
              >
                {copied ? (
                  <span className="text-green-400 text-xs">تم النسخ!</span>
                ) : (
                  <Copy className="w-4 h-4" />
                )}
              </button>
            </div>
          </div>

          {/* Ban Message */}
          <div className="bg-red-500/10 border border-red-500/20 rounded-xl p-4 mb-4">
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 bg-red-600 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <Ban className="w-3 h-3 text-white" />
              </div>
              <div className="flex-1">
                <h3 className="text-red-400 font-semibold mb-1 text-sm">حساب محظور</h3>
                <p className="text-red-300 text-xs leading-relaxed">
                  تم حظر حسابك من قبل الإدارة. إذا كنت تعتقد أن هذا خطأ، يرجى التواصل مع الدعم الفني.
                </p>
              </div>
            </div>
          </div>

          {/* WhatsApp Contact */}
          <div className="bg-green-500/10 border border-green-500/20 rounded-xl p-4 mb-4">
            <h3 className="text-green-400 font-semibold mb-2 text-sm flex items-center gap-2">
              <MessageCircle className="w-4 h-4" />
              التواصل مع الدعم
            </h3>
            <p className="text-green-300 text-xs mb-3 leading-relaxed">
              تواصل معنا عبر واتساب لمعرفة سبب الحظر وإمكانية إعادة التفعيل
            </p>
            
            <button
              onClick={handleWhatsAppContact}
              className="w-full bg-green-600 hover:bg-green-700 text-white py-3 px-4 rounded-lg transition-colors font-medium flex items-center justify-center gap-2 text-sm"
            >
              <MessageCircle className="w-4 h-4" />
              استفسار عبر واتساب
            </button>
          </div>

          {/* Important Info */}
          <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-xl p-4">
            <div className="flex items-start gap-3">
              <AlertTriangle className="w-4 h-4 text-yellow-400 flex-shrink-0 mt-0.5" />
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-xs text-yellow-300">
                  <span>احتفظ بمعرف المستخدم للمراسلات</span>
                </div>
                <div className="flex items-center gap-2 text-xs text-yellow-300">
                  <span>تواصل مع الدعم لمعرفة سبب الحظر</span>
                </div>
                <div className="flex items-center gap-2 text-xs text-yellow-300">
                  <span>قد تحتاج لإنشاء حساب جديد حسب نوع المخالفة</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
