import { NextRequest, NextResponse } from 'next/server'
import { DatabaseManager } from '../../../../../lib/services/database-manager'

// GET - Export table data
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const tableName = searchParams.get('table')

    if (!tableName) {
      return NextResponse.json(
        { error: 'Table name is required' },
        { status: 400 }
      )
    }

    const data = await DatabaseManager.exportTable(tableName)

    // Return as downloadable JSON file
    const filename = `${tableName}_export_${new Date().toISOString().split('T')[0]}.json`
    
    return new NextResponse(JSON.stringify(data, null, 2), {
      headers: {
        'Content-Type': 'application/json',
        'Content-Disposition': `attachment; filename="${filename}"`,
      },
    })
  } catch (error: any) {
    console.error('❌ Admin API: Error exporting table:', error)
    return NextResponse.json(
      { error: 'Failed to export table' },
      { status: 500 }
    )
  }
}
