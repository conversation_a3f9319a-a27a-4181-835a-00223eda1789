'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'

export default function SignupPage() {
  const router = useRouter()

  useEffect(() => {
    // Redirect to the new unified auth page with signup mode
    router.replace('/auth?mode=signup')
  }, [router])

  return (
    <div className="min-h-screen flex items-center justify-center px-4 py-8">
      <div className="text-center text-white">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600 mx-auto mb-4"></div>
        <p>جاري التوجيه إلى صفحة إنشاء الحساب...</p>
      </div>
    </div>
  )
}

