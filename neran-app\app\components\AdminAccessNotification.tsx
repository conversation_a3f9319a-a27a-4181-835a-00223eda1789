'use client'

import { useEffect, useState } from 'react'
import { Shield, RefreshCw, Settings, Users, BarChart3 } from 'lucide-react'
import { useSupabaseAuth } from '../../contexts/SupabaseAuthContext'
import { useRouter } from 'next/navigation'

export default function AdminAccessNotification() {
  const { user, refreshUserData } = useSupabaseAuth()
  const router = useRouter()
  const [showAdminPrompt, setShowAdminPrompt] = useState(false)
  const [isRefreshing, setIsRefreshing] = useState(false)

  useEffect(() => {
    // Show admin prompt if user is admin but not on admin page
    if (user?.role === 'admin' && typeof window !== 'undefined') {
      const currentPath = window.location.pathname
      const isOnAdminPage = currentPath.startsWith('/admin')
      
      // Show prompt if admin is not on admin page and hasn't dismissed it recently
      if (!isOnAdminPage) {
        const dismissedTime = localStorage.getItem('admin_prompt_dismissed')
        const now = Date.now()
        const oneHour = 60 * 60 * 1000
        
        if (!dismissedTime || (now - parseInt(dismissedTime)) > oneHour) {
          setShowAdminPrompt(true)
        }
      }
    }
  }, [user?.role])

  const handleGoToAdmin = () => {
    setShowAdminPrompt(false)
    router.push('/admin')
  }

  const handleRefreshAndGoToAdmin = async () => {
    setIsRefreshing(true)
    try {
      await refreshUserData()
      setTimeout(() => {
        router.push('/admin')
      }, 1000)
    } catch (error) {
      console.error('Error refreshing user data:', error)
      router.push('/admin')
    }
  }

  const handleDismiss = () => {
    setShowAdminPrompt(false)
    localStorage.setItem('admin_prompt_dismissed', Date.now().toString())
  }

  if (!showAdminPrompt || !user || user.role !== 'admin') return null

  return (
    <div className="fixed bottom-4 right-4 z-50 max-w-sm">
      <div className="bg-gradient-to-r from-blue-600 to-blue-700 border border-blue-500 rounded-lg p-4 shadow-2xl">
        <div className="flex items-start gap-3">
          <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0">
            <Shield className="w-4 h-4 text-white" />
          </div>
          
          <div className="flex-1">
            <h3 className="text-white font-semibold mb-1">
              🛡️ لوحة التحكم الإدارية
            </h3>
            <p className="text-blue-100 text-sm mb-3">
              لديك صلاحيات إدارية. هل تريد الوصول إلى لوحة التحكم؟
            </p>
            
            {/* Admin Features Preview */}
            <div className="grid grid-cols-3 gap-2 mb-3">
              <div className="bg-blue-500/30 rounded p-2 text-center">
                <Users className="w-4 h-4 text-blue-200 mx-auto mb-1" />
                <span className="text-xs text-blue-200">المستخدمين</span>
              </div>
              <div className="bg-blue-500/30 rounded p-2 text-center">
                <BarChart3 className="w-4 h-4 text-blue-200 mx-auto mb-1" />
                <span className="text-xs text-blue-200">التقارير</span>
              </div>
              <div className="bg-blue-500/30 rounded p-2 text-center">
                <Settings className="w-4 h-4 text-blue-200 mx-auto mb-1" />
                <span className="text-xs text-blue-200">الإعدادات</span>
              </div>
            </div>
            
            <div className="flex gap-2">
              <button
                onClick={handleGoToAdmin}
                disabled={isRefreshing}
                className="bg-white text-blue-700 px-3 py-1.5 rounded text-sm font-medium hover:bg-blue-50 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-1 flex-1"
              >
                <Shield className="w-3 h-3" />
                لوحة التحكم
              </button>
              
              <button
                onClick={handleRefreshAndGoToAdmin}
                disabled={isRefreshing}
                className="bg-blue-500 text-white px-3 py-1.5 rounded text-sm font-medium hover:bg-blue-400 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-1"
                title="تحديث البيانات والانتقال"
              >
                <RefreshCw className={`w-3 h-3 ${isRefreshing ? 'animate-spin' : ''}`} />
              </button>
            </div>
            
            <button
              onClick={handleDismiss}
              className="text-blue-200 hover:text-white text-xs mt-2 w-full text-center"
            >
              إخفاء لمدة ساعة
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
