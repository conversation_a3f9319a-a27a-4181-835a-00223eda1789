/** @type {import('next').NextConfig} */
const nextConfig = {
  // Performance optimizations for SSR
  experimental: {
    // Enable modern bundling optimizations
    optimizePackageImports: ['lucide-react', '@tanstack/react-query'],
  },

  // Server external packages for better SSR performance
  serverExternalPackages: [],

  // Image optimization
  images: {
    formats: ['image/webp', 'image/avif'],
    unoptimized: true,
    // Add image domains if needed
    domains: [],
  },

  // Compression
  compress: true,

  // Optimize bundle size
  webpack: (config, { dev, isServer }) => {
    // Handle server-side externals to prevent client-side code from running on server
    if (isServer) {
      // Externalize problematic packages for server-side rendering
      config.externals = config.externals || []

      // Properly externalize realtime-js
      if (Array.isArray(config.externals)) {
        config.externals.push(({ request }, callback) => {
          if (request === '@supabase/realtime-js') {
            return callback(null, 'commonjs ' + request)
          }
          callback()
        })
      }

      // Add resolve alias to prevent realtime-js from being bundled
      config.resolve.alias = {
        ...config.resolve.alias,
        '@supabase/realtime-js': false,
      }
    }

    return config
  },

  // Headers for security and performance
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
          {
            key: 'X-DNS-Prefetch-Control',
            value: 'on',
          },
        ],
      },
      // Static assets caching
      {
        source: '/_next/static/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
      // NO API CACHING - Removed to ensure fresh data
      // Manifest.json headers
      {
        source: '/manifest.json',
        headers: [
          {
            key: 'Content-Type',
            value: 'application/manifest+json',
          },
          {
            key: 'Cache-Control',
            value: 'public, max-age=86400',
          },
        ],
      },
    ]
  },

  // ESLint and TypeScript configurations for production
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
}

export default nextConfig
