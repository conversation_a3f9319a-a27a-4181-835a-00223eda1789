import { NextRequest, NextResponse } from 'next/server'

/**
 * Debug API Route for Admin Dashboard
 * Helps diagnose environment and configuration issues in Vercel
 */
export async function GET(request: NextRequest) {
  try {
    // Only allow in development or when debug is enabled
    if (process.env.NODE_ENV === 'production' && process.env.NEXT_PUBLIC_ENABLE_DEBUG !== 'true') {
      return NextResponse.json(
        { error: 'Debug endpoint disabled in production' },
        { status: 403 }
      )
    }

    const debugInfo = {
      timestamp: new Date().toISOString(),
      environment: {
        NODE_ENV: process.env.NODE_ENV,
        VERCEL: process.env.VERCEL,
        VERCEL_ENV: process.env.VERCEL_ENV,
        VERCEL_REGION: process.env.VERCEL_REGION,
      },
      supabase: {
        url: process.env.NEXT_PUBLIC_SUPABASE_URL ? 'Set' : 'Missing',
        anonKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'Set' : 'Missing',
        serviceKey: process.env.SUPABASE_SERVICE_ROLE_KEY ? 'Set' : 'Missing',
      },
      debug: {
        enableDebug: process.env.NEXT_PUBLIC_ENABLE_DEBUG,
        disableLogs: process.env.NEXT_PUBLIC_DISABLE_LOGS,
      },
      cookies: {
        available: request.cookies.getAll().map(c => ({
          name: c.name,
          hasValue: !!c.value
        })),
        count: request.cookies.getAll().length
      },
      headers: {
        userAgent: request.headers.get('user-agent'),
        host: request.headers.get('host'),
        origin: request.headers.get('origin'),
        referer: request.headers.get('referer'),
      }
    }

    console.log('🔍 Debug Info:', debugInfo)

    return NextResponse.json(debugInfo, {
      headers: {
        'Cache-Control': 'no-cache',
        'Content-Type': 'application/json',
      },
    })
  } catch (error: any) {
    console.error('❌ Debug API Error:', error)
    
    return NextResponse.json(
      { 
        error: 'Debug endpoint failed',
        message: error.message,
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}
