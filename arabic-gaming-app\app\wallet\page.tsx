"use client"

import { Wallet, Plus, ArrowUpRight, ArrowDownLeft, CreditCard, DollarSign } from "lucide-react"
import { useUser } from "../context/UserContext"
import CopyButton from "../components/CopyButton"

export default function WalletPage() {
  const { currentUser, orders } = useUser()

  if (!currentUser) {
    return (
      <div className="container mx-auto px-4 py-8 max-w-4xl min-h-screen">
        <div className="text-center text-white">يرجى تسجيل الدخول أولاً</div>
      </div>
    )
  }

  const userOrders = orders.filter((order) => order.userId === currentUser.id)
  const completedOrders = userOrders.filter((order) => order.status === "completed")
  const monthlySpending = completedOrders.reduce((sum, order) => sum + order.price, 0)

  // Mock transaction history
  const transactions = [
    { type: "purchase", title: "شراء جواهر PUBG", amount: -11.25, date: "اليوم 14:30", status: "مكتمل" },
    { type: "deposit", title: "شحن الرصيد", amount: +50.0, date: "أمس 09:15", status: "مكتمل" },
    { type: "purchase", title: "عضوية Free Fire", amount: -6.75, date: "منذ يومين", status: "مكتمل" },
    { type: "refund", title: "استرداد طلب", amount: +16.0, date: "منذ 3 أيام", status: "مكتمل" },
    { type: "purchase", title: "بوياه باس", amount: -6.9, date: "منذ أسبوع", status: "مكتمل" },
  ]

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl min-h-screen">
      {/* Page Title */}
      <div className="text-center mb-8">
        <h1 className="text-3xl md:text-4xl font-bold text-white mb-2">المحفظة الإلكترونية</h1>
        <p className="text-gray-400">إدارة رصيدك ومعاملاتك المالية</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Wallet Balance & Actions */}
        <div className="lg:col-span-2 space-y-6">
          {/* Balance Card */}
          <div className="bg-gradient-to-r from-red-800 to-red-500 rounded-lg p-6 text-white">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-3">
                <Wallet className="w-8 h-8" />
                <div>
                  <h2 className="text-lg font-semibold">الرصيد الحالي</h2>
                  <p className="text-white/80 text-sm">متاح للاستخدام</p>
                </div>
              </div>
              <div className="text-right">
                <div className="text-xs text-white/60 mb-1">معرف المستخدم</div>
                <div className="text-sm font-mono flex items-center gap-1">
                  {currentUser.id}
                  <CopyButton text={currentUser.id} size="sm" className="text-white/80 hover:text-white" />
                </div>
              </div>
            </div>
            <div className="text-3xl font-bold mb-2">${currentUser.balance.toFixed(2)}</div>
            <div className="text-white/80 text-sm">آخر تحديث: منذ 5 دقائق</div>
          </div>

          {/* Balance Update Instructions */}
          <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-6">
            <h3 className="text-yellow-400 font-semibold mb-3 flex items-center gap-2">
              <Plus className="w-5 h-5" />
              كيفية شحن الرصيد
            </h3>
            <div className="space-y-2 text-gray-300">
              <p>للحصول على رصيد إضافي، يرجى التواصل معنا عبر واتساب وتقديم:</p>
              <ul className="list-disc list-inside space-y-1 text-sm">
                <li className="flex items-center gap-2">
                  معرف المستخدم:
                  <span className="font-mono text-yellow-400 flex items-center gap-1">
                    {currentUser.id}
                    <CopyButton text={currentUser.id} size="sm" />
                  </span>
                </li>
                <li>المبلغ المطلوب شحنه</li>
                <li>طريقة الدفع المفضلة</li>
              </ul>
            </div>
          </div>

          {/* Transaction History */}
          <div className="bg-gray-800 border border-gray-600 rounded-lg p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-bold text-white">سجل المعاملات</h2>
              <button className="text-red-400 hover:text-red-300 text-sm">عرض الكل</button>
            </div>

            <div className="space-y-4">
              {transactions.map((transaction, index) => (
                <div key={index} className="bg-gray-900 border border-gray-600 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div
                        className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                          transaction.type === "purchase"
                            ? "bg-red-500/20"
                            : transaction.type === "deposit"
                              ? "bg-green-500/20"
                              : "bg-blue-500/20"
                        }`}
                      >
                        {transaction.type === "purchase" ? (
                          <ArrowUpRight className={`w-5 h-5 text-red-400`} />
                        ) : transaction.type === "deposit" ? (
                          <ArrowDownLeft className={`w-5 h-5 text-green-400`} />
                        ) : (
                          <ArrowDownLeft className={`w-5 h-5 text-blue-400`} />
                        )}
                      </div>
                      <div>
                        <h3 className="text-white font-medium">{transaction.title}</h3>
                        <p className="text-gray-400 text-sm">{transaction.date}</p>
                      </div>
                    </div>
                    <div className="text-left">
                      <div className={`font-bold ${transaction.amount > 0 ? "text-green-400" : "text-red-400"}`}>
                        {transaction.amount > 0 ? "+" : ""}${Math.abs(transaction.amount).toFixed(2)}
                      </div>
                      <div className="text-xs text-gray-400">{transaction.status}</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Payment Methods */}
          <div className="bg-gray-800 border border-gray-600 rounded-lg p-6">
            <h3 className="text-lg font-bold text-white mb-4">طرق الدفع المتاحة</h3>
            <div className="space-y-3">
              <div className="bg-gray-900 border border-gray-600 rounded-lg p-3">
                <div className="flex items-center gap-3">
                  <CreditCard className="w-5 h-5 text-red-400" />
                  <div>
                    <div className="text-white text-sm font-medium">بطاقة ائتمانية</div>
                    <div className="text-gray-400 text-xs">Visa, Mastercard</div>
                  </div>
                </div>
              </div>
              <div className="bg-gray-900 border border-gray-600 rounded-lg p-3">
                <div className="flex items-center gap-3">
                  <DollarSign className="w-5 h-5 text-red-400" />
                  <div>
                    <div className="text-white text-sm font-medium">تحويل بنكي</div>
                    <div className="text-gray-400 text-xs">جميع البنوك المحلية</div>
                  </div>
                </div>
              </div>
              <div className="bg-gray-900 border border-gray-600 rounded-lg p-3">
                <div className="flex items-center gap-3">
                  <Wallet className="w-5 h-5 text-red-400" />
                  <div>
                    <div className="text-white text-sm font-medium">محافظ رقمية</div>
                    <div className="text-gray-400 text-xs">STC Pay, Apple Pay</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Wallet Stats */}
          <div className="bg-gray-800 border border-gray-600 rounded-lg p-6">
            <h3 className="text-lg font-bold text-white mb-4">إحصائيات الشهر</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-gray-300">إجمالي الإنفاق</span>
                <span className="text-red-400 font-semibold">${monthlySpending.toFixed(2)}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-300">عدد المعاملات</span>
                <span className="text-white font-semibold">{userOrders.length}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-300">متوسط الطلب</span>
                <span className="text-white font-semibold">
                  ${completedOrders.length > 0 ? (monthlySpending / completedOrders.length).toFixed(2) : "0.00"}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-300">الطلبات المكتملة</span>
                <span className="text-green-400 font-semibold">{completedOrders.length}</span>
              </div>
            </div>
          </div>

          {/* Contact Info */}
          <div className="bg-gray-800 border border-gray-600 rounded-lg p-6">
            <h3 className="text-lg font-bold text-white mb-4">تواصل معنا</h3>
            <div className="space-y-3">
              <div className="text-center">
                <div className="text-gray-300 text-sm mb-2">للدعم وشحن الرصيد</div>
                <button className="w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg transition-colors flex items-center justify-center gap-2">
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488" />
                  </svg>
                  واتساب
                </button>
              </div>
              <div className="text-center text-xs text-gray-400 flex items-center justify-center gap-1">
                أرسل معرف المستخدم الخاص بك: {currentUser.id}
                <CopyButton text={currentUser.id} size="sm" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
