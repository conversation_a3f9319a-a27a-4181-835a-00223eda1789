/**
 * Orders Service
 * Handles order-related operations using Supabase
 */

import { createSupabaseBrowserClient } from '../supabase'
import { Order } from '../../types'

export class OrderService {
  private static supabase = createSupabaseBrowserClient()

  /**
   * Get user orders
   */
  static async getUserOrders(userId: string, limit: number = 10): Promise<Order[]> {
    try {
      console.log(`📋 Fetching orders for user: ${userId}`)

      const { data, error } = await this.supabase
        .from('orders')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(limit)

      if (error) {
        console.error('❌ Get user orders error:', error)
        throw new Error('Failed to fetch orders')
      }

      console.log(`✅ Fetched ${data.length} orders for user`)
      return data.map(this.mapSupabaseOrderToAppOrder)
    } catch (error: any) {
      console.error('❌ Get user orders failed:', error)
      throw new Error('حدث خطأ أثناء جلب الطلبات')
    }
  }

  /**
   * Create new order
   */
  static async createOrder(orderData: {
    userId: string
    productId: string
    gameId: string
    amount: string
    price: number
    notes?: string
  }): Promise<Order> {
    try {
      console.log('📝 Creating new order:', orderData)

      const { data, error } = await this.supabase
        .from('orders')
        .insert({
          user_id: orderData.userId,
          product_id: orderData.productId,
          game_id: orderData.gameId,
          amount: orderData.amount,
          price: orderData.price,
          status: 'pending',
          notes: orderData.notes || null
        })
        .select()
        .single()

      if (error) {
        console.error('❌ Create order error:', error)
        throw new Error('Failed to create order')
      }

      console.log('✅ Order created successfully')
      return this.mapSupabaseOrderToAppOrder(data)
    } catch (error: any) {
      console.error('❌ Create order failed:', error)
      throw new Error('حدث خطأ أثناء إنشاء الطلب')
    }
  }

  /**
   * Get order by ID
   */
  static async getOrderById(orderId: string): Promise<Order | null> {
    try {
      const { data, error } = await this.supabase
        .from('orders')
        .select('*')
        .eq('id', orderId)
        .single()

      if (error) {
        if (error.code === 'PGRST116') {
          return null // Order not found
        }
        throw error
      }

      return this.mapSupabaseOrderToAppOrder(data)
    } catch (error: any) {
      console.error('❌ Get order by ID failed:', error)
      throw new Error('حدث خطأ أثناء جلب الطلب')
    }
  }

  /**
   * Update order status
   */
  static async updateOrderStatus(
    orderId: string,
    status: 'pending' | 'processing' | 'completed' | 'cancelled',
    adminNotes?: string
  ): Promise<void> {
    try {
      const updates: any = {
        status,
        updated_at: new Date().toISOString()
      }

      if (adminNotes) {
        updates.admin_notes = adminNotes
      }

      if (status === 'completed') {
        updates.completed_at = new Date().toISOString()
      }

      const { error } = await this.supabase
        .from('orders')
        .update(updates)
        .eq('id', orderId)

      if (error) {
        console.error('❌ Update order status error:', error)
        throw new Error('Failed to update order status')
      }

      console.log('✅ Order status updated successfully')
    } catch (error: any) {
      console.error('❌ Update order status failed:', error)
      throw new Error('حدث خطأ أثناء تحديث حالة الطلب')
    }
  }

  /**
   * Map Supabase order to app order format
   */
  private static mapSupabaseOrderToAppOrder(order: any): Order {
    return {
      id: order.id,
      userId: order.user_id,
      gameId: order.game_id,
      product: order.product_name || 'Unknown Product',
      productId: order.product_id,
      amount: order.amount,
      price: order.price,
      status: order.status,
      createdAt: order.created_at,
      updatedAt: order.updated_at,
      completedAt: order.completed_at,
      notes: order.notes,
      adminNotes: order.admin_notes
    }
  }
}
