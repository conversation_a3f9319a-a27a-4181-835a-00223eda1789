"use client"

import { useState } from "react"
import { Check, X, Search, Eye } from "lucide-react"
import { useUser } from "../../context/UserContext"

export default function OrderManagement() {
  const { orders, updateOrderStatus, users } = useUser()
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<"all" | "completed" | "pending" | "cancelled">("all")
  const [selectedOrder, setSelectedOrder] = useState<any>(null)

  const filteredOrders = orders.filter((order) => {
    const user = users.find((u) => u.id === order.userId)
    const matchesSearch =
      order.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.product.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.gameId.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user?.name.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === "all" || order.status === statusFilter
    return matchesSearch && matchesStatus
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-500/20 text-green-400"
      case "pending":
        return "bg-yellow-500/20 text-yellow-400"
      case "cancelled":
        return "bg-red-500/20 text-red-400"
      default:
        return "bg-gray-500/20 text-gray-400"
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case "completed":
        return "مكتمل"
      case "pending":
        return "قيد المعالجة"
      case "cancelled":
        return "ملغي"
      default:
        return "غير محدد"
    }
  }

  return (
    <div className="space-y-6">
      <div className="bg-gray-800 border border-gray-600 rounded-lg p-6">
        <div className="flex flex-col md:flex-row md:items-center justify-between gap-4 mb-6">
          <h2 className="text-xl font-bold text-white">إدارة الطلبات</h2>

          {/* Search and Filter */}
          <div className="flex flex-col md:flex-row gap-3">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="البحث في الطلبات..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-4 pr-10 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white text-sm focus:outline-none focus:ring-2 focus:ring-red-800"
              />
            </div>

            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value as any)}
              className="px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white text-sm focus:outline-none focus:ring-2 focus:ring-red-800"
            >
              <option value="all">جميع الحالات</option>
              <option value="pending">قيد المعالجة</option>
              <option value="completed">مكتمل</option>
              <option value="cancelled">ملغي</option>
            </select>
          </div>
        </div>

        {/* Mobile View */}
        <div className="space-y-3 md:hidden">
          {filteredOrders.map((order) => {
            const user = users.find((u) => u.id === order.userId)
            return (
              <div key={order.id} className="bg-gray-900 border border-gray-600 rounded-lg p-3">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-red-400 font-medium text-sm">{order.id}</span>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
                    {getStatusText(order.status)}
                  </span>
                </div>
                <div className="text-white text-sm mb-1">{order.product}</div>
                <div className="text-gray-400 text-xs mb-1">العميل: {user?.name || "غير معروف"}</div>
                <div className="text-gray-400 text-xs mb-2">Game ID: {order.gameId}</div>
                <div className="flex items-center justify-between">
                  <span className="text-white font-semibold">${order.price}</span>
                  <div className="flex items-center gap-1">
                    <button
                      onClick={() => setSelectedOrder(order)}
                      className="p-1 hover:bg-gray-700 rounded transition-colors"
                      title="عرض التفاصيل"
                    >
                      <Eye className="w-3 h-3 text-gray-400" />
                    </button>
                    <button
                      onClick={() =>
                        updateOrderStatus(order.id, order.status === "completed" ? "pending" : "completed")
                      }
                      className="p-1 hover:bg-gray-700 rounded transition-colors"
                      title="تغيير الحالة"
                    >
                      <Check className="w-3 h-3 text-green-400" />
                    </button>
                    <button
                      onClick={() => updateOrderStatus(order.id, "cancelled")}
                      className="p-1 hover:bg-gray-700 rounded transition-colors"
                      title="إلغاء الطلب"
                    >
                      <X className="w-3 h-3 text-red-400" />
                    </button>
                  </div>
                </div>
              </div>
            )
          })}
        </div>

        {/* Desktop View */}
        <div className="hidden md:block overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-600">
                <th className="text-right py-3 text-gray-300 font-medium">رقم الطلب</th>
                <th className="text-right py-3 text-gray-300 font-medium">العميل</th>
                <th className="text-right py-3 text-gray-300 font-medium">المنتج</th>
                <th className="text-right py-3 text-gray-300 font-medium">Game ID</th>
                <th className="text-right py-3 text-gray-300 font-medium">المبلغ</th>
                <th className="text-right py-3 text-gray-300 font-medium">التاريخ</th>
                <th className="text-right py-3 text-gray-300 font-medium">الحالة</th>
                <th className="text-right py-3 text-gray-300 font-medium">الإجراءات</th>
              </tr>
            </thead>
            <tbody>
              {filteredOrders.map((order) => {
                const user = users.find((u) => u.id === order.userId)
                return (
                  <tr key={order.id} className="border-b border-gray-600/50">
                    <td className="py-4 text-red-400 font-medium">{order.id}</td>
                    <td className="py-4 text-white">{user?.name || "غير معروف"}</td>
                    <td className="py-4 text-gray-300">{order.product}</td>
                    <td className="py-4 text-gray-400">{order.gameId}</td>
                    <td className="py-4 text-white font-semibold">${order.price}</td>
                    <td className="py-4 text-gray-400">{order.date}</td>
                    <td className="py-4">
                      <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
                        {getStatusText(order.status)}
                      </span>
                    </td>
                    <td className="py-4">
                      <div className="flex items-center gap-2">
                        <button
                          onClick={() => setSelectedOrder(order)}
                          className="p-1 hover:bg-gray-700 rounded transition-colors"
                          title="عرض التفاصيل"
                        >
                          <Eye className="w-4 h-4 text-gray-400" />
                        </button>
                        <button
                          onClick={() =>
                            updateOrderStatus(order.id, order.status === "completed" ? "pending" : "completed")
                          }
                          className="p-1 hover:bg-gray-700 rounded transition-colors"
                          title="تغيير الحالة"
                        >
                          <Check className="w-4 h-4 text-green-400" />
                        </button>
                        <button
                          onClick={() => updateOrderStatus(order.id, "cancelled")}
                          className="p-1 hover:bg-gray-700 rounded transition-colors"
                          title="إلغاء الطلب"
                        >
                          <X className="w-4 h-4 text-red-400" />
                        </button>
                      </div>
                    </td>
                  </tr>
                )
              })}
            </tbody>
          </table>
        </div>

        {filteredOrders.length === 0 && (
          <div className="text-center py-8">
            <p className="text-gray-400">لا توجد طلبات تطابق البحث</p>
          </div>
        )}
      </div>

      {/* Order Details Modal */}
      {selectedOrder && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
          <div className="bg-gray-800 border border-gray-600 rounded-lg p-6 max-w-md w-full">
            <h3 className="text-lg font-bold text-white mb-4">تفاصيل الطلب</h3>
            <div className="space-y-3">
              <div>
                <span className="text-gray-400">رقم الطلب: </span>
                <span className="text-white">{selectedOrder.id}</span>
              </div>
              <div>
                <span className="text-gray-400">المنتج: </span>
                <span className="text-white">{selectedOrder.product}</span>
              </div>
              <div>
                <span className="text-gray-400">Game ID: </span>
                <span className="text-white">{selectedOrder.gameId}</span>
              </div>
              <div>
                <span className="text-gray-400">المبلغ: </span>
                <span className="text-white">${selectedOrder.price}</span>
              </div>
              <div>
                <span className="text-gray-400">التاريخ: </span>
                <span className="text-white">{selectedOrder.date}</span>
              </div>
              <div>
                <span className="text-gray-400">الحالة: </span>
                <span className={`px-2 py-1 rounded text-xs ${getStatusColor(selectedOrder.status)}`}>
                  {getStatusText(selectedOrder.status)}
                </span>
              </div>
            </div>
            <div className="flex gap-3 mt-6">
              <button
                onClick={() => setSelectedOrder(null)}
                className="flex-1 bg-gray-700 hover:bg-gray-600 text-white py-2 px-4 rounded transition-colors"
              >
                إغلاق
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
