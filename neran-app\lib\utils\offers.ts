import { Product } from '../../types'
import { SSRProduct } from '../ssr-helpers'

// Union type for products with offers
export type ProductWithOffer = Product | SSRProduct

/**
 * Check if a product has an active offer
 * @param product - Product to check
 * @returns boolean indicating if offer is active
 */
export function isOfferActive(product: ProductWithOffer): boolean {
  if (!product.hasOffer) return false

  const now = new Date()

  try {
    // Check start date
    if (product.offerStartDate) {
      const startDate = new Date(product.offerStartDate)

      if (!isNaN(startDate.getTime()) && now < startDate) {
        return false
      }
    }

    // Check end date
    if (product.offerEndDate) {
      const endDate = new Date(product.offerEndDate)

      if (!isNaN(endDate.getTime()) && now > endDate) {
        return false
      }
    }

    return true
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      console.error('Error checking offer active status:', error)
    }
    return false
  }
}

/**
 * Calculate the effective price for a product (considering active offers)
 * @param product - Product to calculate price for
 * @returns The effective price to charge
 */
export function getEffectivePrice(product: ProductWithOffer): number {
  if (!isOfferActive(product)) {
    return product.price
  }
  
  if (product.discountPercentage && product.originalPrice) {
    const discountAmount = (product.originalPrice * product.discountPercentage) / 100
    return Math.max(0, product.originalPrice - discountAmount)
  }
  
  return product.price
}

/**
 * Calculate the discount amount for a product
 * @param product - Product to calculate discount for
 * @returns The discount amount in currency
 */
export function getDiscountAmount(product: ProductWithOffer): number {
  if (!isOfferActive(product) || !product.originalPrice || !product.discountPercentage) {
    return 0
  }
  
  return (product.originalPrice * product.discountPercentage) / 100
}

/**
 * Calculate the savings amount for a product
 * @param product - Product to calculate savings for
 * @returns The savings amount in currency
 */
export function getSavingsAmount(product: ProductWithOffer): number {
  if (!isOfferActive(product) || !product.originalPrice) {
    return 0
  }
  
  const effectivePrice = getEffectivePrice(product)
  return product.originalPrice - effectivePrice
}

/**
 * Get the display price information for a product
 * @param product - Product to get price info for
 * @returns Object with price display information
 */
export function getPriceDisplayInfo(product: ProductWithOffer) {
  const hasActiveOffer = isOfferActive(product)
  const effectivePrice = getEffectivePrice(product)
  const savingsAmount = getSavingsAmount(product)
  
  return {
    hasActiveOffer,
    currentPrice: effectivePrice,
    originalPrice: hasActiveOffer ? product.originalPrice : null,
    discountPercentage: hasActiveOffer ? product.discountPercentage : null,
    savingsAmount: hasActiveOffer ? savingsAmount : 0,
    displayPrice: effectivePrice,
  }
}

/**
 * Format offer dates for display
 * @param product - Product with offer dates
 * @returns Formatted date strings
 */
export function getOfferDateInfo(product: ProductWithOffer) {
  if (!product.hasOffer) return null
  
  const formatDate = (date: string | Date | undefined) => {
    if (!date) return null

    const dateObj = typeof date === 'string' ? new Date(date) : date
    return dateObj.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      calendar: 'gregory'
    })
  }
  
  return {
    startDate: formatDate(product.offerStartDate),
    endDate: formatDate(product.offerEndDate),
  }
}

/**
 * Validate offer data before saving
 * @param offerData - Offer data to validate
 * @returns Validation result with errors if any
 */
export function validateOfferData(offerData: {
  hasOffer: boolean
  originalPrice?: number
  discountPercentage?: number
  offerStartDate?: Date
  offerEndDate?: Date
}) {
  const errors: string[] = []
  
  if (!offerData.hasOffer) {
    return { isValid: true, errors: [] }
  }
  
  // Validate original price
  if (!offerData.originalPrice || offerData.originalPrice <= 0) {
    errors.push('السعر الأصلي مطلوب ويجب أن يكون أكبر من صفر')
  }
  
  // Validate discount percentage
  if (!offerData.discountPercentage || offerData.discountPercentage <= 0 || offerData.discountPercentage > 100) {
    errors.push('نسبة الخصم مطلوبة ويجب أن تكون بين 1 و 100')
  }
  
  // Validate dates
  if (offerData.offerStartDate && offerData.offerEndDate) {
    if (offerData.offerStartDate >= offerData.offerEndDate) {
      errors.push('تاريخ انتهاء العرض يجب أن يكون بعد تاريخ البداية')
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  }
}

/**
 * Create offer badge text for display
 * @param product - Product with offer
 * @returns Badge text in Arabic
 */
export function getOfferBadgeText(product: ProductWithOffer): string | null {
  if (!isOfferActive(product)) return null

  return 'عرض'
}

/**
 * Check if offer is expiring soon (within 24 hours)
 * @param product - Product to check
 * @returns boolean indicating if offer expires soon
 */
export function isOfferExpiringSoon(product: ProductWithOffer): boolean {
  if (!isOfferActive(product) || !product.offerEndDate) return false
  
  const endDate = typeof product.offerEndDate === 'string'
    ? new Date(product.offerEndDate)
    : product.offerEndDate
  
  const now = new Date()
  const timeDiff = endDate.getTime() - now.getTime()
  const hoursUntilExpiry = timeDiff / (1000 * 60 * 60)
  
  return hoursUntilExpiry <= 24 && hoursUntilExpiry > 0
}

/**
 * Get the base price for a product based on user role
 * @param product - Product to get price for
 * @param userRole - User role ('user', 'admin', 'distributor')
 * @returns The base price based on role
 */
export function getRoleBasedPrice(product: ProductWithOffer, userRole: 'user' | 'admin' | 'distributor' = 'user'): number {
  // Distributors get special pricing, admins and users get regular pricing
  if (userRole === 'distributor' && product.distributorPrice) {
    return product.distributorPrice
  }
  return product.price
}

/**
 * Calculate the effective price for a product (considering role and active offers)
 * @param product - Product to calculate price for
 * @param userRole - User role ('user', 'admin', 'distributor')
 * @returns The effective price to charge
 */
export function getEffectivePriceByRole(product: ProductWithOffer, userRole: 'user' | 'admin' | 'distributor' = 'user'): number {
  // PRIORITY 1: Distributors always get their special price, regardless of offers
  if (userRole === 'distributor' && product.distributorPrice) {
    return product.distributorPrice
  }
  
  // PRIORITY 2: For regular users and admins, apply offers if available
  const basePrice = getRoleBasedPrice(product, userRole)
  
  // If no active offer, return the role-based price
  if (!isOfferActive(product)) {
    return basePrice
  }
  
  // Apply offers to the original price for regular users/admins
  if (product.discountPercentage && product.originalPrice) {
    const discountAmount = (product.originalPrice * product.discountPercentage) / 100
    const offerPrice = Math.max(0, product.originalPrice - discountAmount)
    
    // Return the better price for the user (lower of offer price or base price)
    return Math.min(offerPrice, basePrice)
  }
  
  return basePrice
}
