import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseServerClient } from '../../../../../lib/supabase-server'
import { supabaseAdmin } from '../../../../../lib/supabase-admin'
import { v4 as uuidv4 } from 'uuid'

export async function POST(request: NextRequest) {
  try {
    console.log('🖼️ Product Image Upload: Starting image upload request')

    // Check admin authentication
    const supabase = await createSupabaseServerClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      console.log('❌ Product Image Upload: No authenticated user found')
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Check if user is admin
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('role, status')
      .eq('id', user.id)
      .single()

    if (profileError || !profile || profile.role !== 'admin' || profile.status !== 'active') {
      console.log('❌ Product Image Upload: User is not an active admin')
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      )
    }

    console.log('✅ Product Image Upload: Admin authentication successful')

    // Parse form data
    const formData = await request.formData()
    const file = formData.get('image') as File

    if (!file) {
      return NextResponse.json(
        { error: 'No image file provided' },
        { status: 400 }
      )
    }

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { error: 'Invalid file type. Only JPEG, PNG, and WebP are allowed.' },
        { status: 400 }
      )
    }

    // Validate file size (max 5MB)
    const maxSize = 5 * 1024 * 1024 // 5MB
    if (file.size > maxSize) {
      return NextResponse.json(
        { error: 'File size too large. Maximum 5MB allowed.' },
        { status: 400 }
      )
    }

    console.log(`📁 Product Image Upload: Processing file - ${file.name}, ${file.size} bytes`)

    // Generate unique filename
    const fileExtension = file.name.split('.').pop()
    const uniqueFilename = `product-${uuidv4()}.${fileExtension}`
    
    // Convert file to buffer for Supabase Storage
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)

    // Upload to Supabase Storage
    const { data: uploadData, error: uploadError } = await supabaseAdmin.storage
      .from('product-images')
      .upload(uniqueFilename, buffer, {
        contentType: file.type,
        upsert: false
      })

    if (uploadError) {
      console.error('❌ Supabase Storage upload error:', uploadError)
      return NextResponse.json(
        { error: 'Failed to upload image to storage' },
        { status: 500 }
      )
    }

    // Get public URL for the uploaded image
    const { data: { publicUrl } } = supabaseAdmin.storage
      .from('product-images')
      .getPublicUrl(uniqueFilename)

    console.log(`✅ Product Image Upload: File uploaded successfully - ${publicUrl}`)

    return NextResponse.json({
      success: true,
      imageUrl: publicUrl,
      filename: uniqueFilename,
      originalName: file.name,
      size: file.size,
      type: file.type
    })

  } catch (error: any) {
    console.error('❌ Product Image Upload: Error uploading image:', error)
    return NextResponse.json(
      { error: 'Failed to upload image' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    console.log('🗑️ Product Image Delete: Starting image deletion request')

    // Check admin authentication
    const supabase = await createSupabaseServerClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Check if user is admin
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('role, status')
      .eq('id', user.id)
      .single()

    if (profileError || !profile || profile.role !== 'admin' || profile.status !== 'active') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      )
    }

    const { imageUrl } = await request.json()

    if (!imageUrl) {
      return NextResponse.json(
        { error: 'No image URL provided' },
        { status: 400 }
      )
    }

    // Extract filename from Supabase URL
    // URL format: https://[project].supabase.co/storage/v1/object/public/product-images/filename.ext
    const urlParts = imageUrl.split('/')
    const filename = urlParts[urlParts.length - 1]
    
    if (!filename) {
      return NextResponse.json(
        { error: 'Invalid image URL' },
        { status: 400 }
      )
    }

    // Delete file from Supabase Storage
    const { error: deleteError } = await supabaseAdmin.storage
      .from('product-images')
      .remove([filename])

    if (deleteError) {
      console.error('❌ Supabase Storage delete error:', deleteError)
      // Don't fail the request if file doesn't exist
      console.log(`⚠️ Product Image Delete: File not found or already deleted - ${filename}`)
    } else {
      console.log(`✅ Product Image Delete: File deleted successfully - ${filename}`)
    }

    return NextResponse.json({
      success: true,
      message: 'Image deleted successfully'
    })

  } catch (error: any) {
    console.error('❌ Product Image Delete: Error deleting image:', error)
    return NextResponse.json(
      { error: 'Failed to delete image' },
      { status: 500 }
    )
  }
} 