import { NextRequest, NextResponse } from 'next/server'
import { DatabaseManager } from '../../../../../lib/services/database-manager'

export async function GET(request: NextRequest) {
  try {
    const stats = await DatabaseManager.getDatabaseStats()

    return NextResponse.json(stats, {
      headers: {
        'Cache-Control': 'public, max-age=30, s-maxage=30', // 30 seconds cache
        'Content-Type': 'application/json',
      },
    })
  } catch (error: any) {
    // Only log errors in development
    if (process.env.NODE_ENV === 'development') {
      console.error('❌ Admin API: Error fetching database stats:', error)
    }
    return NextResponse.json(
      { error: 'Failed to fetch database statistics' },
      { status: 500 }
    )
  }
}
