import { NextRequest, NextResponse } from 'next/server'
import { SupabaseAdminService } from '../../../../lib/services/supabase-admin'
import { createSupabaseServerClient } from '../../../../lib/supabase-server'

export async function GET(request: NextRequest) {
  try {
    console.log('🔧 Admin API: Starting user fetch request')

    // Check admin authentication using session-based approach
    const supabase = await createSupabaseServerClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      console.log('❌ Admin API: No authenticated user found')
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Check if user is admin
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('role, status')
      .eq('id', user.id)
      .single()

    if (profileError || !profile || profile.role !== 'admin' || profile.status !== 'active') {
      console.log('❌ Admin API: User is not an active admin:', { 
        role: profile?.role, 
        status: profile?.status 
      })
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      )
    }

    console.log('✅ Admin API: Admin authentication successful for user:', user.id)

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')

    console.log(`🔧 Admin API: Fetching users - page: ${page}, limit: ${limit}`)

    // Get users with pagination using server-side admin service
    const users = await SupabaseAdminService.getAllUsers(page, limit)

    console.log(`✅ Admin API: Users fetched successfully - count: ${users.length}`)

    // Disable caching for real-time updates
    return NextResponse.json(users, {
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
        'Content-Type': 'application/json',
      },
    })
  } catch (error: any) {
    console.error('❌ Admin API: Error fetching users:', error)

    // Return empty array instead of error to prevent admin dashboard from breaking
    console.log('⚠️ Admin API: Returning empty users array due to error')

    return NextResponse.json([], {
      headers: {
        'Cache-Control': 'no-cache', // Don't cache error responses
        'Content-Type': 'application/json',
        'X-Fallback-Data': 'true', // Indicate this is fallback data
      },
    })
  }
}

export async function PATCH(request: NextRequest) {
  try {
    const { userId, action, data } = await request.json()
    console.log(`🔧 Admin API: Performing ${action} on user ${userId}`)

    // Get current admin user for protection and audit trail
    let currentAdminId = null
    try {
      const supabase = await createSupabaseServerClient()
      const { data: { user } } = await supabase.auth.getUser()
      currentAdminId = user?.id || null
    } catch (error) {
      console.warn('Could not get current admin user:', error)
    }

    // Prevent actions on current logged-in admin
    if (currentAdminId && userId === currentAdminId) {
      if (action === 'ban' || action === 'makeUser' || action === 'delete') {
        console.log(`❌ Admin API: Blocked ${action} on current admin ${userId}`)
        return NextResponse.json(
          { error: 'لا يمكنك تنفيذ هذا الإجراء على حسابك الحالي' },
          { status: 403 }
        )
      }
    }

    let result
    switch (action) {
      case 'approve':
        await SupabaseAdminService.updateUserStatus(userId, 'active')
        result = { success: true, message: 'User approved successfully', userId, status: 'active' }
        break
      case 'ban':
        // If banning an admin, auto-demote to user first
        const userToBan = await SupabaseAdminService.getUserById(userId)
        if (userToBan?.role === 'admin') {
          console.log(`🔄 Admin API: Auto-demoting admin ${userId} to user before banning`)
          await SupabaseAdminService.updateUserRole(userId, 'user')
        }
        await SupabaseAdminService.updateUserStatus(userId, 'suspended')
        result = { 
          success: true, 
          message: 'User banned successfully', 
          userId, 
          status: 'suspended',
          role: userToBan?.role === 'admin' ? 'user' : userToBan?.role
        }
        break
      case 'unban':
        await SupabaseAdminService.updateUserStatus(userId, 'active')
        result = { success: true, message: 'User unbanned successfully', userId, status: 'active' }
        break
      case 'makeAdmin':
        await SupabaseAdminService.updateUserRole(userId, 'admin')
        result = { success: true, message: 'User promoted to admin successfully', userId, role: 'admin' }
        break
      case 'makeUser':
        await SupabaseAdminService.updateUserRole(userId, 'user')
        result = { success: true, message: 'Admin demoted to user successfully', userId, role: 'user' }
        break
      case 'makeDistributor':
        console.log(`🔄 Admin API: Converting user ${userId} to distributor role`)
        try {
          await SupabaseAdminService.updateUserRole(userId, 'distributor')
          console.log(`✅ Admin API: Successfully converted user ${userId} to distributor`)
          result = { success: true, message: 'User converted to distributor successfully', userId, role: 'distributor' }
        } catch (updateError: any) {
          console.error(`❌ Admin API: Failed to convert user ${userId} to distributor:`, updateError)
          throw updateError
        }
        break
      case 'updateBalance':
        await SupabaseAdminService.updateUserBalance(
          userId, 
          data.balance, 
          currentAdminId || undefined, 
          'تعديل الرصيد من لوحة الإدارة'
        )
        result = { success: true, message: 'User balance updated successfully', userId, balance: data.balance }
        break
      default:
        throw new Error(`Unknown action: ${action}`)
    }

    console.log(`✅ Admin API: ${action} completed successfully`)

    return NextResponse.json(result)
  } catch (error: any) {
    console.error('❌ Admin API: Error updating user:', error)
    return NextResponse.json(
      { error: 'Failed to update user' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')
    
    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      )
    }

    // Get current admin user for protection
    let currentAdminId = null
    try {
      const supabase = await createSupabaseServerClient()
      const { data: { user } } = await supabase.auth.getUser()
      currentAdminId = user?.id || null
    } catch (error) {
      console.warn('Could not get current admin user:', error)
    }

    // Prevent deleting current logged-in admin
    if (currentAdminId && userId === currentAdminId) {
      console.log(`❌ Admin API: Blocked deletion of current admin ${userId}`)
      return NextResponse.json(
        { error: 'لا يمكنك حذف حسابك الحالي' },
        { status: 403 }
      )
    }

    console.log(`🔧 Admin API: Deleting user ${userId}`)

    await SupabaseAdminService.deleteUser(userId)
    
    console.log('✅ Admin API: User deleted successfully')
    
    return NextResponse.json({ success: true })
  } catch (error: any) {
    console.error('❌ Admin API: Error deleting user:', error)
    return NextResponse.json(
      { error: 'Failed to delete user' },
      { status: 500 }
    )
  }
}
