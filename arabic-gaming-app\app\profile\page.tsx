"use client"

import { useState } from "react"
import { User, Edit, Shield, Clock, Star, DollarSign } from "lucide-react"
import { useUser } from "../context/UserContext"
import CopyButton from "../components/CopyButton"

export default function ProfilePage() {
  const { currentUser, orders } = useUser()
  const [isEditing, setIsEditing] = useState(false)

  if (!currentUser) {
    return (
      <div className="container mx-auto px-4 py-8 max-w-4xl min-h-screen">
        <div className="text-center text-white">يرجى تسجيل الدخول أولاً</div>
      </div>
    )
  }

  const userOrders = orders.filter((order) => order.userId === currentUser.id)
  const completedOrders = userOrders.filter((order) => order.status === "completed")

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl min-h-screen">
      {/* Page Title */}
      <div className="text-center mb-8">
        <h1 className="text-3xl md:text-4xl font-bold text-white mb-2">الملف الشخصي</h1>
        <p className="text-gray-400">إدارة معلوماتك الشخصية وإعداداتك</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Profile Info Card */}
        <div className="lg:col-span-2">
          <div className="bg-gray-800 border border-gray-600 rounded-lg p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-bold text-white">المعلومات الشخصية</h2>
              <button
                onClick={() => setIsEditing(!isEditing)}
                className="flex items-center gap-2 bg-red-800 hover:bg-red-900 text-white px-4 py-2 rounded-lg transition-colors"
              >
                <Edit className="w-4 h-4" />
                {isEditing ? "حفظ" : "تعديل"}
              </button>
            </div>

            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">الاسم الكامل</label>
                  <input
                    type="text"
                    value={currentUser.name}
                    className="w-full px-4 py-3 bg-gray-900 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-red-800"
                    readOnly={!isEditing}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">البريد الإلكتروني</label>
                  <input
                    type="email"
                    value={currentUser.email}
                    className="w-full px-4 py-3 bg-gray-900 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-red-800"
                    readOnly={!isEditing}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">معرف المستخدم</label>
                  <div className="relative">
                    <input
                      type="text"
                      value={currentUser.id}
                      className="w-full px-4 py-3 pr-12 bg-gray-900 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-red-800"
                      readOnly
                    />
                    <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
                      <CopyButton text={currentUser.id} size="md" />
                    </div>
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">تاريخ التسجيل</label>
                  <input
                    type="text"
                    value={new Date(currentUser.createdAt).toLocaleDateString("ar-SA")}
                    className="w-full px-4 py-3 bg-gray-900 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-red-800"
                    readOnly
                  />
                </div>
              </div>

              <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4">
                <h3 className="text-yellow-400 font-semibold mb-2 flex items-center gap-2">
                  تعديل الرصيد
                  <CopyButton text={currentUser.id} size="sm" className="ml-2" />
                </h3>
                <p className="text-gray-300 text-sm">
                  لتعديل رصيدك، يرجى التواصل معنا عبر واتساب وتقديم معرف المستخدم الخاص بك:{" "}
                  <strong className="font-mono">{currentUser.id}</strong>
                </p>
              </div>
            </div>
          </div>

          {/* Recent Orders */}
          <div className="bg-gray-800 border border-gray-600 rounded-lg p-6 mt-6">
            <h2 className="text-xl font-bold text-white mb-6">الطلبات الأخيرة</h2>
            <div className="space-y-4">
              {userOrders.length === 0 ? (
                <p className="text-gray-400">لا توجد طلبات حتى الآن</p>
              ) : (
                userOrders.slice(0, 5).map((order) => (
                  <div key={order.id} className="bg-gray-900 border border-gray-600 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="flex items-center gap-2 mb-1">
                          <span className="text-red-400 font-semibold">{order.id}</span>
                          <span className="text-gray-400">•</span>
                          <span className="text-white">{order.product}</span>
                        </div>
                        <div className="text-sm text-gray-400">
                          Game ID: {order.gameId} - ${order.price}
                        </div>
                      </div>
                      <div className="text-left">
                        <div
                          className={`px-3 py-1 rounded-full text-xs font-medium mb-1 ${
                            order.status === "completed"
                              ? "bg-green-500/20 text-green-400"
                              : order.status === "pending"
                                ? "bg-yellow-500/20 text-yellow-400"
                                : "bg-red-500/20 text-red-400"
                          }`}
                        >
                          {order.status === "completed"
                            ? "مكتمل"
                            : order.status === "pending"
                              ? "قيد المعالجة"
                              : "ملغي"}
                        </div>
                        <div className="text-xs text-gray-400">{order.date}</div>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        </div>

        {/* Stats Sidebar */}
        <div className="space-y-6">
          {/* Profile Stats */}
          <div className="bg-gray-800 border border-gray-600 rounded-lg p-6">
            <div className="text-center mb-6">
              <div className="w-20 h-20 bg-red-800 rounded-full flex items-center justify-center mx-auto mb-4">
                <User className="w-10 h-10 text-white" />
              </div>
              <h3 className="text-lg font-bold text-white">{currentUser.name}</h3>
              <p className="text-gray-400">عضو مميز</p>
              <div className="flex items-center justify-center gap-1 mt-2">
                <span className="text-gray-400 text-sm">ID: {currentUser.id}</span>
                <CopyButton text={currentUser.id} size="sm" />
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Shield className="w-4 h-4 text-red-400" />
                  <span className="text-gray-300">الرصيد</span>
                </div>
                <span className="text-white font-semibold">${currentUser.balance.toFixed(2)}</span>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Star className="w-4 h-4 text-red-400" />
                  <span className="text-gray-300">الحالة</span>
                </div>
                <span
                  className={`font-semibold ${currentUser.status === "active" ? "text-green-400" : "text-red-400"}`}
                >
                  {currentUser.status === "active" ? "نشط" : "معلق"}
                </span>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Clock className="w-4 h-4 text-red-400" />
                  <span className="text-gray-300">إجمالي الطلبات</span>
                </div>
                <span className="text-white font-semibold">{userOrders.length}</span>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <DollarSign className="w-4 h-4 text-red-400" />
                  <span className="text-gray-300">إجمالي الإنفاق</span>
                </div>
                <span className="text-white font-semibold">
                  ${completedOrders.reduce((sum, order) => sum + order.price, 0).toFixed(2)}
                </span>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="bg-gray-800 border border-gray-600 rounded-lg p-6">
            <h3 className="text-lg font-bold text-white mb-4">إجراءات سريعة</h3>
            <div className="space-y-3">
              <button className="w-full bg-red-800 hover:bg-red-900 text-white py-2 px-4 rounded-lg transition-colors">
                تغيير كلمة المرور
              </button>
              <button className="w-full bg-gray-900 hover:bg-gray-700 border border-gray-600 text-white py-2 px-4 rounded-lg transition-colors">
                تحميل الفواتير
              </button>
              <button className="w-full bg-gray-900 hover:bg-gray-700 border border-gray-600 text-white py-2 px-4 rounded-lg transition-colors">
                الدعم الفني
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
