"use client"

import { useState } from "react"
import PlayerIdInput from "./components/PlayerIdInput"
import OfferCards from "./components/OfferCards"
import PurchaseButton from "./components/PurchaseButton"
import { useUser } from "./context/UserContext"

export default function HomePage() {
  const [selectedOffer, setSelectedOffer] = useState<any>(null)
  const [playerId, setPlayerId] = useState("")
  const { addOrder, currentUser } = useUser()

  const handlePurchase = () => {
    if (!selectedOffer || !playerId || !currentUser) {
      alert("يرجى اختيار العرض وإدخال ايدي اللاعب")
      return
    }

    if (currentUser.balance < selectedOffer.price) {
      alert("رصيدك غير كافي لإتمام هذه العملية")
      return
    }

    // Add order
    addOrder({
      userId: currentUser.id,
      gameId: playerId,
      product: selectedOffer.title,
      amount: selectedOffer.title,
      price: selectedOffer.price,
      status: "pending",
    })

    alert("تم إرسال طلبك بنجاح! سيتم معالجته قريباً")
    setSelectedOffer(null)
    setPlayerId("")
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl min-h-screen">
      {/* Main Title */}
      <div className="text-center mb-8">
        <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-4">نيران كارد - شحن فوري وآمن</h1>
        <p className="text-gray-400 text-lg mb-2">أفضل موقع لشحن بطاقات الألعاب والتطبيقات</p>
        <p className="text-red-400 text-sm">يتم الشحن في ثواني معدودة</p>
      </div>

      {/* Player ID Input Section */}
      <div className="mb-8">
        <PlayerIdInput playerId={playerId} setPlayerId={setPlayerId} />
      </div>

      {/* Offers Section */}
      <div className="mb-8">
        <h2 className="text-2xl md:text-3xl font-bold text-white mb-6 text-center">إختر العرض المناسب لك :</h2>
        <OfferCards selectedOffer={selectedOffer} setSelectedOffer={setSelectedOffer} />
      </div>

      {/* Purchase Button */}
      <div className="text-center">
        <PurchaseButton onPurchase={handlePurchase} disabled={!selectedOffer || !playerId} />
      </div>
    </div>
  )
}
