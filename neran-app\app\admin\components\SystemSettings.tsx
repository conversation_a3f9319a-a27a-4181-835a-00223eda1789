"use client"

import { useState, useEffect } from "react"
import { Save, RefreshCw, Globe, Database, CheckCircle, AlertCircle, Download, Upload, Trash2, HardDrive, FileText, Play, AlertTriangle, Copy } from "lucide-react"
// Simplified system settings - removed Firebase dependencies

export default function SystemSettings() {
  const [settings, setSettings] = useState({
    maintenanceMode: false,
    allowRegistrations: true,
    autoApproveUsers: true,
    supportWhatsApp: "+966501234567",
    supportEmail: "<EMAIL>",
    language: "ar",
  })

  const [databaseStats, setDatabaseStats] = useState({
    tables: [],
    totalSize: '0 MB',
    totalRows: 0,
    uptime: 'N/A',
    connections: 0,
    performance: {
      avgQueryTime: 0,
      slowQueries: 0,
      cacheHitRatio: 0
    }
  })

  const [backups, setBackups] = useState<any[]>([])
  const [selectedTables, setSelectedTables] = useState<string[]>([])
  const [backupName, setBackupName] = useState('')
  const [sqlQuery, setSqlQuery] = useState('SELECT * FROM user_profiles LIMIT 10;')
  const [queryResult, setQueryResult] = useState<any>(null)
  const [showBackupDialog, setShowBackupDialog] = useState(false)
  const [showRestoreDialog, setShowRestoreDialog] = useState(false)
  const [selectedBackup, setSelectedBackup] = useState<any>(null)

  const [activeTab, setActiveTab] = useState("general")
  const [loading, setLoading] = useState(false)
  const [saveStatus, setSaveStatus] = useState<'idle' | 'saving' | 'success' | 'error'>('idle')
  const [dbLoading, setDbLoading] = useState(true)
  const [actionLoading, setActionLoading] = useState<string | null>(null)
  const [backupLoading, setBackupLoading] = useState(false)
  const [queryLoading, setQueryLoading] = useState(false)

  // Load settings from database
  useEffect(() => {
    const loadSettings = async () => {
      try {
        const response = await fetch('/api/settings')
        if (response.ok) {
          const currentSettings = await response.json()

          // Update state with current settings from database
          setSettings({
            maintenanceMode: currentSettings.maintenanceMode || false,
            allowRegistrations: currentSettings.allowRegistrations || true,
            autoApproveUsers: currentSettings.autoApproveUsers !== undefined ? currentSettings.autoApproveUsers : true,
            supportWhatsApp: currentSettings.supportWhatsApp || "+966501234567",
            supportEmail: currentSettings.supportEmail || "<EMAIL>",
            language: currentSettings.language || "ar",
          })
        }
      } catch (error) {
        console.error('Error loading settings:', error)
        // Keep default settings if loading fails
      }
    }

    loadSettings()

    // Load database stats
    loadDatabaseStats()
    loadBackups()
    setDbLoading(false)
  }, [])

  const handleSaveSettings = async () => {
    setLoading(true)
    setSaveStatus('saving')

    try {
      console.log('Settings to save:', settings)

      // Save settings to database via API
      const response = await fetch('/api/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(settings)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to save settings')
      }

      const result = await response.json()
      console.log('✅ Settings saved successfully:', result)

      setSaveStatus('success')
      setTimeout(() => setSaveStatus('idle'), 3000)
    } catch (error) {
      console.error('Error saving settings:', error)
      setSaveStatus('error')
      setTimeout(() => setSaveStatus('idle'), 3000)
    } finally {
      setLoading(false)
    }
  }

  // Load database statistics
  const loadDatabaseStats = async () => {
    try {
      const response = await fetch('/api/admin/database/stats')
      if (response.ok) {
        const stats = await response.json()
        setDatabaseStats(stats)
      }
    } catch (error) {
      console.error('Error loading database stats:', error)
    }
  }

  // Load backups list
  const loadBackups = async () => {
    try {
      const response = await fetch('/api/admin/database/backup')
      if (response.ok) {
        const backupsList = await response.json()
        setBackups(backupsList)
      }
    } catch (error) {
      console.error('Error loading backups:', error)
    }
  }

  // Create backup
  const handleCreateBackup = async () => {
    if (!backupName.trim()) {
      alert('يرجى إدخال اسم النسخة الاحتياطية')
      return
    }

    setBackupLoading(true)
    try {
      const response = await fetch('/api/admin/database/backup', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: backupName,
          tables: selectedTables.length > 0 ? selectedTables : undefined
        })
      })

      if (response.ok) {
        alert('✅ تم إنشاء النسخة الاحتياطية بنجاح')
        setBackupName('')
        setSelectedTables([])
        setShowBackupDialog(false)
        loadBackups()
      } else {
        throw new Error('Failed to create backup')
      }
    } catch (error) {
      console.error('Error creating backup:', error)
      alert('❌ حدث خطأ أثناء إنشاء النسخة الاحتياطية')
    } finally {
      setBackupLoading(false)
    }
  }

  // Restore backup
  const handleRestoreBackup = async (backupId: string, overwrite = false) => {
    if (!confirm('هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟ قد يؤثر هذا على البيانات الحالية.')) {
      return
    }

    setActionLoading('restore')
    try {
      const response = await fetch('/api/admin/database/restore', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ backupId, overwrite })
      })

      if (response.ok) {
        alert('✅ تم استعادة النسخة الاحتياطية بنجاح')
        setShowRestoreDialog(false)
        loadDatabaseStats()
      } else {
        throw new Error('Failed to restore backup')
      }
    } catch (error) {
      console.error('Error restoring backup:', error)
      alert('❌ حدث خطأ أثناء استعادة النسخة الاحتياطية')
    } finally {
      setActionLoading(null)
    }
  }

  // Delete backup
  const handleDeleteBackup = async (backupId: string) => {
    if (!confirm('هل أنت متأكد من حذف هذه النسخة الاحتياطية؟')) {
      return
    }

    try {
      const response = await fetch(`/api/admin/database/backup?id=${backupId}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        alert('✅ تم حذف النسخة الاحتياطية بنجاح')
        loadBackups()
      } else {
        throw new Error('Failed to delete backup')
      }
    } catch (error) {
      console.error('Error deleting backup:', error)
      alert('❌ حدث خطأ أثناء حذف النسخة الاحتياطية')
    }
  }

  // Export table
  const handleExportTable = async (tableName: string) => {
    try {
      const response = await fetch(`/api/admin/database/export?table=${tableName}`)
      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `${tableName}_export.json`
        a.click()
        window.URL.revokeObjectURL(url)
      } else {
        throw new Error('Failed to export table')
      }
    } catch (error) {
      console.error('Error exporting table:', error)
      alert('❌ حدث خطأ أثناء تصدير الجدول')
    }
  }

  // Clear table
  const handleClearTable = async (tableName: string) => {
    const confirmText = `هل أنت متأكد من حذف جميع البيانات من جدول ${tableName}؟ هذا الإجراء لا يمكن التراجع عنه!`
    if (!confirm(confirmText)) {
      return
    }

    try {
      const response = await fetch('/api/admin/database/clear', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ tableName, confirm: true })
      })

      if (response.ok) {
        const result = await response.json()
        alert(`✅ ${result.message}`)
        loadDatabaseStats()
      } else {
        throw new Error('Failed to clear table')
      }
    } catch (error) {
      // Only log errors in development
      if (process.env.NODE_ENV === 'development') {
        console.error('Error clearing table:', error)
      }
      alert('❌ حدث خطأ أثناء حذف البيانات')
    }
  }

  // Execute SQL query
  const handleExecuteQuery = async () => {
    if (!sqlQuery.trim()) {
      alert('يرجى إدخال استعلام SQL')
      return
    }

    setQueryLoading(true)
    try {
      const response = await fetch('/api/admin/database/query', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ query: sqlQuery })
      })

      if (response.ok) {
        const result = await response.json()
        setQueryResult(result)
      } else {
        const error = await response.json()
        alert(`❌ خطأ في الاستعلام: ${error.error}`)
      }
    } catch (error) {
      // Only log errors in development
      if (process.env.NODE_ENV === 'development') {
        console.error('Error executing query:', error)
      }
      alert('❌ حدث خطأ أثناء تنفيذ الاستعلام')
    } finally {
      setQueryLoading(false)
    }
  }

  const handleDatabaseAction = async (action: 'export' | 'import') => {
    setActionLoading(action)

    try {
      if (action === 'export') {
        // Export all data as backup
        const timestamp = new Date().toISOString().split('T')[0]
        await handleCreateBackup()
      } else {
        alert('استيراد قاعدة البيانات متاح من خلال استعادة النسخ الاحتياطية')
      }
    } catch (error) {
      // Only log errors in development
      if (process.env.NODE_ENV === 'development') {
        console.error(`Error performing ${action}:`, error)
      }
      alert(`❌ حدث خطأ أثناء تنفيذ العملية`)
    } finally {
      setActionLoading(null)
    }
  }

  const handleResetSettings = async () => {
    if (confirm("هل أنت متأكد من إعادة تعيين جميع الإعدادات؟")) {
      const defaultSettings = {
        maintenanceMode: false,
        allowRegistrations: true,
        autoApproveUsers: true,
        supportWhatsApp: "+966501234567",
        supportEmail: "<EMAIL>",
        language: "ar",
      }
      setSettings(defaultSettings)
      setSaveStatus('idle')
    }
  }

  const tabs = [
    { id: "general", label: "عام", icon: Globe },
    { id: "database", label: "قاعدة البيانات", icon: Database },
  ]

  const getSaveButtonText = () => {
    switch (saveStatus) {
      case 'saving': return 'جاري الحفظ...'
      case 'success': return 'تم الحفظ ✓'
      case 'error': return 'خطأ في الحفظ'
      default: return 'حفظ الإعدادات'
    }
  }

  const getSaveButtonClass = () => {
    const baseClass = "px-4 py-2 rounded-lg transition-colors flex items-center gap-2 font-medium"
    switch (saveStatus) {
      case 'saving': return `${baseClass} bg-gray-600 text-white cursor-not-allowed`
      case 'success': return `${baseClass} bg-green-600 text-white`
      case 'error': return `${baseClass} bg-red-600 text-white`
      default: return `${baseClass} bg-red-600 hover:bg-red-700 text-white`
    }
  }

  return (
    <div className="space-y-4">
      <div className="bg-gray-800 border border-gray-600 rounded-xl p-4 md:p-6">
        <div className="flex flex-col md:flex-row md:items-center justify-between gap-4 mb-6">
          <div>
            <h2 className="text-lg md:text-xl font-bold text-white">إعدادات النظام</h2>
            <p className="text-gray-400 text-sm mt-1">إدارة إعدادات المنصة والتحكم في النظام</p>
          </div>
          <div className="flex flex-col sm:flex-row gap-3">
            <button
              onClick={handleResetSettings}
              disabled={loading}
              className="bg-gray-700 hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg transition-colors flex items-center justify-center gap-2 font-medium"
            >
              <RefreshCw className="w-4 h-4" />
              إعادة تعيين
            </button>
            <button
              onClick={handleSaveSettings}
              disabled={loading}
              className={getSaveButtonClass()}
            >
              {saveStatus === 'saving' ? (
                <RefreshCw className="w-4 h-4 animate-spin" />
              ) : saveStatus === 'success' ? (
                <CheckCircle className="w-4 h-4" />
              ) : saveStatus === 'error' ? (
                <AlertCircle className="w-4 h-4" />
              ) : (
                <Save className="w-4 h-4" />
              )}
              {getSaveButtonText()}
            </button>
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-600">
          <div className="flex space-x-8 overflow-x-auto">
          {tabs.map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center gap-2 py-3 px-4 border-b-2 transition-colors whitespace-nowrap ${
                  activeTab === tab.id
                      ? "border-red-600 text-red-400"
                      : "border-transparent text-gray-400 hover:text-gray-300"
                }`}
              >
                <Icon className="w-4 h-4" />
                {tab.label}
              </button>
            )
          })}
          </div>
        </div>

        {/* General Settings */}
        {activeTab === "general" && (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">واتساب الدعم</label>
                  <input
                    type="tel"
                    value={settings.supportWhatsApp}
                  onChange={(e) => setSettings({ ...settings, supportWhatsApp: e.target.value })}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-red-800"
                  />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">بريد الدعم</label>
                <input
                  type="email"
                  value={settings.supportEmail}
                  onChange={(e) => setSettings({ ...settings, supportEmail: e.target.value })}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-red-800"
                />
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 bg-gray-900 border border-gray-700 rounded-xl hover:bg-gray-850 transition-colors">
                <div>
                  <div className="text-white font-medium">وضع الصيانة</div>
                  <div className="text-gray-400 text-sm">تعطيل الموقع مؤقتاً للصيانة والتحديثات</div>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={settings.maintenanceMode}
                    onChange={(e) => setSettings({ ...settings, maintenanceMode: e.target.checked })}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-red-600 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-600"></div>
                </label>
              </div>

              <div className="flex items-center justify-between p-4 bg-gray-900 border border-gray-700 rounded-xl hover:bg-gray-850 transition-colors">
                <div>
                  <div className="text-white font-medium">السماح بالتسجيل</div>
                  <div className="text-gray-400 text-sm">السماح للمستخدمين الجدد بإنشاء حسابات</div>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={settings.allowRegistrations}
                    onChange={(e) => setSettings({ ...settings, allowRegistrations: e.target.checked })}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-red-600 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-600"></div>
                </label>
              </div>

              <div className="flex items-center justify-between p-4 bg-gray-900 border border-gray-700 rounded-xl hover:bg-gray-850 transition-colors">
                <div>
                  <div className="text-white font-medium">الموافقة التلقائية للمستخدمين</div>
                  <div className="text-gray-400 text-sm">الموافقة على المستخدمين الجدد تلقائياً دون مراجعة إدارية</div>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={settings.autoApproveUsers}
                    onChange={(e) => setSettings({ ...settings, autoApproveUsers: e.target.checked })}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-red-600 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-600"></div>
                </label>
              </div>
            </div>
          </div>
        )}

        {/* Database Settings */}
        {activeTab === "database" && (
          <div className="space-y-6">
            {dbLoading ? (
              <div className="text-center py-8">
                <div className="text-white">🔄 جاري تحميل إحصائيات قاعدة البيانات...</div>
              </div>
            ) : (
              <>
                {/* Database Overview */}
                <div className="bg-gray-900 border border-gray-600 rounded-xl p-4">
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="text-white font-semibold">نظرة عامة على قاعدة البيانات</h4>
                    <button
                      onClick={loadDatabaseStats}
                      className="flex items-center gap-2 text-blue-400 hover:text-blue-300 transition-colors"
                    >
                      <RefreshCw className="w-4 h-4" />
                      تحديث
                    </button>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div className="text-center p-4 bg-gray-800 rounded-lg">
                      <div className="text-2xl font-bold text-blue-400">{databaseStats.totalRows.toLocaleString()}</div>
                      <div className="text-gray-400 text-sm">إجمالي السجلات</div>
                    </div>
                    <div className="text-center p-4 bg-gray-800 rounded-lg">
                      <div className="text-2xl font-bold text-green-400">{databaseStats.totalSize}</div>
                      <div className="text-gray-400 text-sm">حجم قاعدة البيانات</div>
                    </div>
                    <div className="text-center p-4 bg-gray-800 rounded-lg">
                      <div className="text-2xl font-bold text-purple-400">{databaseStats.tables.length}</div>
                      <div className="text-gray-400 text-sm">عدد الجداول</div>
                    </div>
                    <div className="text-center p-4 bg-gray-800 rounded-lg">
                      <div className="text-2xl font-bold text-green-400">{databaseStats.performance.cacheHitRatio}%</div>
                      <div className="text-gray-400 text-sm">معدل نجاح التخزين المؤقت</div>
                    </div>
                  </div>
                </div>

                {/* Tables Information */}
                <div className="bg-gray-900 border border-gray-600 rounded-xl p-4">
                  <h4 className="text-white font-semibold mb-4">معلومات الجداول</h4>
                  <div className="overflow-x-auto">
                    <table className="w-full text-sm">
                      <thead>
                        <tr className="border-b border-gray-700">
                          <th className="text-right text-gray-300 py-2">اسم الجدول</th>
                          <th className="text-right text-gray-300 py-2">عدد السجلات</th>
                          <th className="text-right text-gray-300 py-2">الحجم</th>
                          <th className="text-right text-gray-300 py-2">آخر تعديل</th>
                          <th className="text-right text-gray-300 py-2">الإجراءات</th>
                        </tr>
                      </thead>
                      <tbody>
                        {databaseStats.tables.map((table: any) => (
                          <tr key={table.name} className="border-b border-gray-800">
                            <td className="text-white py-3 font-medium">{table.name}</td>
                            <td className="text-gray-300 py-3">{table.rowCount.toLocaleString()}</td>
                            <td className="text-gray-300 py-3">{table.size}</td>
                            <td className="text-gray-300 py-3">{new Date(table.lastModified).toLocaleDateString('ar')}</td>
                            <td className="py-3">
                              <div className="flex gap-2">
                                <button
                                  onClick={() => handleExportTable(table.name)}
                                  className="text-blue-400 hover:text-blue-300 transition-colors"
                                  title="تصدير الجدول"
                                >
                                  <Download className="w-4 h-4" />
                                </button>
                                <button
                                  onClick={() => handleClearTable(table.name)}
                                  className="text-red-400 hover:text-red-300 transition-colors"
                                  title="حذف جميع البيانات"
                                >
                                  <Trash2 className="w-4 h-4" />
                                </button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>

                {/* Backup Management */}
                <div className="bg-gray-900 border border-gray-600 rounded-xl p-4">
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="text-white font-semibold">إدارة النسخ الاحتياطية</h4>
                    <button
                      onClick={() => setShowBackupDialog(true)}
                      className="flex items-center gap-2 bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors"
                    >
                      <HardDrive className="w-4 h-4" />
                      إنشاء نسخة احتياطية
                    </button>
                  </div>

                  <div className="space-y-3">
                    {backups.length === 0 ? (
                      <div className="text-center py-8 text-gray-400">
                        لا توجد نسخ احتياطية متاحة
                      </div>
                    ) : (
                      backups.map((backup: any) => (
                        <div key={backup.id} className="bg-gray-800 rounded-lg p-4 flex items-center justify-between">
                          <div>
                            <div className="text-white font-medium">{backup.name}</div>
                            <div className="text-gray-400 text-sm">
                              {backup.size} • {backup.tables.length} جداول • {new Date(backup.createdAt).toLocaleDateString('ar')}
                            </div>
                            <div className="text-xs text-gray-500 mt-1">
                              {backup.type === 'full' ? 'نسخة كاملة' : 'نسخة جزئية'}
                            </div>
                          </div>
                          <div className="flex gap-2">
                            <button
                              onClick={() => {
                                setSelectedBackup(backup)
                                setShowRestoreDialog(true)
                              }}
                              className="text-blue-400 hover:text-blue-300 transition-colors"
                              title="استعادة"
                            >
                              <Upload className="w-4 h-4" />
                            </button>
                            <button
                              onClick={() => handleDeleteBackup(backup.id)}
                              className="text-red-400 hover:text-red-300 transition-colors"
                              title="حذف"
                            >
                              <Trash2 className="w-4 h-4" />
                            </button>
                          </div>
                        </div>
                      ))
                    )}
                  </div>
                </div>

                {/* SQL Query Interface */}
                <div className="bg-gray-900 border border-gray-600 rounded-xl p-4">
                  <h4 className="text-white font-semibold mb-4">تنفيذ استعلامات SQL</h4>
                  <div className="space-y-4">
                    <div>
                      <label className="block text-gray-300 text-sm mb-2">استعلام SQL (SELECT فقط)</label>
                      <textarea
                        value={sqlQuery}
                        onChange={(e) => setSqlQuery(e.target.value)}
                        className="w-full bg-gray-800 border border-gray-600 rounded-lg px-3 py-2 text-white font-mono text-sm"
                        rows={4}
                        placeholder="SELECT * FROM user_profiles LIMIT 10;"
                      />
                    </div>
                    <div className="flex gap-3">
                      <button
                        onClick={handleExecuteQuery}
                        disabled={queryLoading}
                        className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 disabled:opacity-50 text-white px-4 py-2 rounded-lg transition-colors"
                      >
                        {queryLoading ? (
                          <RefreshCw className="w-4 h-4 animate-spin" />
                        ) : (
                          <Play className="w-4 h-4" />
                        )}
                        تنفيذ الاستعلام
                      </button>
                      <button
                        onClick={() => {
                          setSqlQuery('SELECT * FROM user_profiles LIMIT 10;')
                          setQueryResult(null)
                        }}
                        className="text-gray-400 hover:text-gray-300 transition-colors"
                      >
                        مسح
                      </button>
                    </div>

                    {queryResult && (
                      <div className="bg-gray-800 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-gray-300 text-sm">نتائج الاستعلام</span>
                          <button
                            onClick={() => {
                              navigator.clipboard.writeText(JSON.stringify(queryResult, null, 2))
                              alert('تم نسخ النتائج')
                            }}
                            className="text-blue-400 hover:text-blue-300 transition-colors"
                          >
                            <Copy className="w-4 h-4" />
                          </button>
                        </div>
                        <pre className="text-xs text-gray-300 overflow-x-auto max-h-64">
                          {JSON.stringify(queryResult, null, 2)}
                        </pre>
                      </div>
                    )}
                  </div>
                </div>
              </>
            )}
          </div>
        )}
      </div>

      {/* Backup Creation Dialog */}
      {showBackupDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-900 border border-gray-600 rounded-xl p-6 w-full max-w-md">
            <h3 className="text-white font-semibold mb-4">إنشاء نسخة احتياطية</h3>

            <div className="space-y-4">
              <div>
                <label className="block text-gray-300 text-sm mb-2">اسم النسخة الاحتياطية</label>
                <input
                  type="text"
                  value={backupName}
                  onChange={(e) => setBackupName(e.target.value)}
                  className="w-full bg-gray-800 border border-gray-600 rounded-lg px-3 py-2 text-white"
                  placeholder="نسخة احتياطية - 2024"
                />
              </div>

              <div>
                <label className="block text-gray-300 text-sm mb-2">الجداول المحددة (اختياري)</label>
                <div className="space-y-2 max-h-32 overflow-y-auto">
                  {databaseStats.tables.map((table: any) => (
                    <label key={table.name} className="flex items-center gap-2">
                      <input
                        type="checkbox"
                        checked={selectedTables.includes(table.name)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedTables([...selectedTables, table.name])
                          } else {
                            setSelectedTables(selectedTables.filter(t => t !== table.name))
                          }
                        }}
                        className="rounded"
                      />
                      <span className="text-gray-300 text-sm">{table.name}</span>
                    </label>
                  ))}
                </div>
                <p className="text-gray-500 text-xs mt-2">
                  اتركها فارغة لنسخ جميع الجداول
                </p>
              </div>
            </div>

            <div className="flex gap-3 mt-6">
              <button
                onClick={handleCreateBackup}
                disabled={backupLoading}
                className="flex-1 bg-green-600 hover:bg-green-700 disabled:opacity-50 text-white px-4 py-2 rounded-lg transition-colors"
              >
                {backupLoading ? 'جاري الإنشاء...' : 'إنشاء النسخة'}
              </button>
              <button
                onClick={() => {
                  setShowBackupDialog(false)
                  setBackupName('')
                  setSelectedTables([])
                }}
                className="px-4 py-2 text-gray-400 hover:text-gray-300 transition-colors"
              >
                إلغاء
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Restore Dialog */}
      {showRestoreDialog && selectedBackup && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-900 border border-gray-600 rounded-xl p-6 w-full max-w-md">
            <h3 className="text-white font-semibold mb-4">استعادة النسخة الاحتياطية</h3>

            <div className="space-y-4">
              <div className="bg-gray-800 rounded-lg p-3">
                <div className="text-white font-medium">{selectedBackup.name}</div>
                <div className="text-gray-400 text-sm">
                  {selectedBackup.size} • {selectedBackup.tables.length} جداول
                </div>
              </div>

              <div className="bg-yellow-900 border border-yellow-600 rounded-lg p-3">
                <div className="flex items-center gap-2 text-yellow-400">
                  <AlertTriangle className="w-4 h-4" />
                  <span className="font-medium">تحذير</span>
                </div>
                <p className="text-yellow-300 text-sm mt-1">
                  ستؤثر هذه العملية على البيانات الحالية. تأكد من إنشاء نسخة احتياطية قبل المتابعة.
                </p>
              </div>
            </div>

            <div className="flex gap-3 mt-6">
              <button
                onClick={() => handleRestoreBackup(selectedBackup.id, true)}
                disabled={actionLoading === 'restore'}
                className="flex-1 bg-red-600 hover:bg-red-700 disabled:opacity-50 text-white px-4 py-2 rounded-lg transition-colors"
              >
                {actionLoading === 'restore' ? 'جاري الاستعادة...' : 'استعادة مع الكتابة فوق البيانات'}
              </button>
              <button
                onClick={() => {
                  setShowRestoreDialog(false)
                  setSelectedBackup(null)
                }}
                className="px-4 py-2 text-gray-400 hover:text-gray-300 transition-colors"
              >
                إلغاء
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
