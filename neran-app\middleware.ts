import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

/**
 * Middleware to handle security, authentication, and maintenance mode
 * Note: Using simple JWT verification to avoid Edge Runtime issues
 */
export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // Skip middleware for static files and minimal essential routes only
  if (
    pathname.startsWith('/_next/') ||
    pathname.startsWith('/api/settings') || // Always allow settings API
    pathname.startsWith('/api/auth/') || // Allow auth endpoints
    pathname.startsWith('/api/admin/') || // Allow admin API routes
    pathname.startsWith('/api/user/') || // Allow user profile API (needed for auth)
    pathname.includes('.') || // Static files like images, css, js
    pathname === '/favicon.ico' ||
    pathname === '/manifest.json' ||
    pathname === '/api/health'
  ) {
    return NextResponse.next()
  }

  // Create response object for cookie handling
  const response = NextResponse.next()

  // Allow admin access (authentication handled by pages)
  if (pathname.startsWith('/admin')) {
    // Admin authentication is handled by the admin pages themselves
  }

  try {
    // Fetch system settings to check maintenance mode with no cache
    const settingsResponse = await fetch(new URL('/api/settings', request.url), {
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      },
    })

    if (settingsResponse.ok) {
      const settings = await settingsResponse.json()

      // Check if maintenance mode is enabled
      if (settings.maintenanceMode) {
        // Allow complete admin access during maintenance (pages + APIs)
        if (pathname.startsWith('/admin')) {
          return NextResponse.next()
        }

        // Allow auth/login pages so admins can log in during maintenance
        if (pathname === '/auth' || pathname === '/login' || pathname === '/signup') {
          return NextResponse.next()
        }

        // Redirect ALL other requests to maintenance page (including home page)
        if (pathname !== '/maintenance') {
          console.log(`🔧 Maintenance mode: Redirecting ${pathname} to /maintenance`)
          return NextResponse.redirect(new URL('/maintenance', request.url))
        }
      } else {
        // If not in maintenance mode but user is on maintenance page, redirect to home
        if (pathname === '/maintenance') {
          return NextResponse.redirect(new URL('/', request.url))
        }
      }
    }
  } catch (error) {
    console.error('❌ Middleware: Failed to check maintenance mode:', error)
    // Continue normally if settings fetch fails to avoid breaking the site
  }

  return response
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!_next/static|_next/image|favicon.ico).*)',
  ],
}
