/**
 * Transactions API Route
 * Handles fetching user transactions and creating new transactions
 */

import { NextRequest, NextResponse } from 'next/server'
import { TransactionService } from '../../../lib/services/transactions'
import { createSupabaseServerClient } from '../../../lib/supabase-server'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')
    const limit = parseInt(searchParams.get('limit') || '50')

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      )
    }

    console.log(`📊 Fetching transactions for user: ${userId}`)

    const transactions = await TransactionService.getUserTransactions(userId, limit)

    console.log(`✅ Fetched ${transactions.length} transactions`)

    return NextResponse.json(transactions, {
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate', // No caching for real-time data
        'Pragma': 'no-cache',
        'Expires': '0',
        'Content-Type': 'application/json',
      },
    })
  } catch (error: any) {
    console.error('❌ Transactions API error:', error)
    
    // Return empty array instead of error for graceful handling
    return NextResponse.json([], {
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
        'Content-Type': 'application/json',
      },
    })
  }
}

/**
 * Create new transaction (with authentication)
 */
export async function POST(request: NextRequest) {
  try {
    // 🔒 SECURITY: Authenticate user first
    const supabase = await createSupabaseServerClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      console.log('❌ Unauthorized transaction attempt')
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const transactionData = await request.json()

    console.log('📝 Creating transaction:', transactionData)

    // 🔒 SECURITY: Validate user ID matches authenticated user
    if (transactionData.userId !== user.id) {
      console.log('❌ User ID mismatch - potential security breach attempt')
      return NextResponse.json(
        { error: 'Invalid user ID' },
        { status: 403 }
      )
    }

    // Validate required fields
    if (!transactionData.type || !transactionData.userId || !transactionData.amount) {
      return NextResponse.json(
        { error: 'Missing required fields: type, userId, amount' },
        { status: 400 }
      )
    }

    let transaction
    switch (transactionData.type) {
      case 'purchase':
        transaction = await TransactionService.createOrderPurchaseTransaction(
          transactionData.userId,
          transactionData.orderId || '',
          transactionData.amount,
          transactionData.description || 'Purchase'
        )
        break
      
      default:
        return NextResponse.json(
          { error: 'Invalid transaction type for user API' },
          { status: 400 }
        )
    }

    console.log('✅ Transaction created successfully:', transaction.id)

    return NextResponse.json({
      success: true,
      transaction,
      message: 'Transaction created successfully'
    }, {
      status: 201,
      headers: {
        'Content-Type': 'application/json',
      },
    })
  } catch (error: any) {
    console.error('❌ Transaction creation error:', error)
    return NextResponse.json(
      { error: error.message || 'Failed to create transaction' },
      { status: 500 }
    )
  }
}
