import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '../../../../lib/supabase'

/**
 * User Signin API Route
 * Handles user authentication
 */
export async function POST(request: NextRequest) {
  try {
    const { email, password } = await request.json()

    if (!email || !password) {
      return NextResponse.json(
        { error: 'Email and password are required' },
        { status: 400 }
      )
    }

    console.log('🔑 Starting signin process for:', email)

    // Sign in with Supabase Auth
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email,
      password
    })

    if (authError) {
      console.error('❌ Supabase Auth signin error:', authError)
      return NextResponse.json(
        { error: authError.message },
        { status: 400 }
      )
    }

    if (!authData.user) {
      return NextResponse.json(
        { error: 'No user data returned from signin' },
        { status: 400 }
      )
    }

    // Get user profile from our user_profiles table
    const { data: userData, error: dbError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', authData.user.id)
      .single()

    if (dbError) {
      console.error('❌ Database user fetch error:', dbError)
      return NextResponse.json(
        { error: 'Failed to fetch user profile' },
        { status: 500 }
      )
    }

    console.log('✅ Signin successful')
    
    return NextResponse.json({
      success: true,
      user: {
        id: userData.id,
        email: userData.email,
        name: userData.name,
        role: userData.role,
        status: userData.status,
        balance: userData.balance,
        phone: userData.phone,
        avatar_url: userData.avatar_url,
        created_at: userData.created_at,
        updated_at: userData.updated_at
      },
      session: authData.session
    })

  } catch (error: any) {
    console.error('❌ Signin API error:', error)
    return NextResponse.json(
      { error: error.message || 'حدث خطأ أثناء تسجيل الدخول' },
      { status: 500 }
    )
  }
}
