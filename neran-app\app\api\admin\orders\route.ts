import { NextRequest, NextResponse } from 'next/server'
import { SupabaseAdminService } from '../../../../lib/services/supabase-admin'
import { createSupabaseServerClient } from '../../../../lib/supabase-server'

export async function GET(request: NextRequest) {
  try {
    console.log('🔧 Admin Orders API: Starting orders fetch request')

    // Check admin authentication using session-based approach
    const supabase = await createSupabaseServerClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      console.log('❌ Admin Orders API: No authenticated user found')
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Check if user is admin
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('role, status')
      .eq('id', user.id)
      .single()

    if (profileError || !profile || profile.role !== 'admin' || profile.status !== 'active') {
      console.log('❌ Admin Orders API: User is not an active admin:', { 
        role: profile?.role, 
        status: profile?.status 
      })
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      )
    }

    console.log('✅ Admin Orders API: Admin authentication successful for user:', user.id)

    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status') || undefined
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')

    console.log(`🔧 Admin Orders API: Fetching orders - status: ${status}, page: ${page}, limit: ${limit}`)

    // Get orders with details using server-side admin service
    const orders = await SupabaseAdminService.getOrdersWithDetails(status as any, page, limit)

    console.log(`✅ Admin Orders API: Orders fetched successfully - count: ${orders.length}`)

    // Disable caching for real-time updates
    return NextResponse.json(orders, {
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
        'Content-Type': 'application/json',
      },
    })
  } catch (error: any) {
    console.error('❌ Admin Orders API: Error fetching orders:', error)

    // Return empty array instead of error to prevent admin dashboard from breaking
    console.log('⚠️ Admin Orders API: Returning empty orders array due to error')

    return NextResponse.json([], {
      headers: {
        'Cache-Control': 'no-cache', // Don't cache error responses
        'Content-Type': 'application/json',
        'X-Fallback-Data': 'true', // Indicate this is fallback data
      },
    })
  }
}

export async function PATCH(request: NextRequest) {
  try {
    console.log('🔧 Admin Orders API: Starting order update request')

    // Check admin authentication using session-based approach
    const supabase = await createSupabaseServerClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      console.log('❌ Admin Orders API: No authenticated user found')
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Check if user is admin
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('role, status')
      .eq('id', user.id)
      .single()

    if (profileError || !profile || profile.role !== 'admin' || profile.status !== 'active') {
      console.log('❌ Admin Orders API: User is not an active admin:', { 
        role: profile?.role, 
        status: profile?.status 
      })
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      )
    }

    console.log('✅ Admin Orders API: Admin authentication successful for user:', user.id)

    const { orderId, status, action } = await request.json()

    // Support both status and action parameters for backward compatibility
    let targetStatus = status
    if (action && !status) {
      switch (action) {
        case 'complete':
          targetStatus = 'completed'
          break
        case 'cancel':
          targetStatus = 'cancelled'
          break
        default:
          throw new Error(`Unknown action: ${action}`)
      }
    }

    if (!targetStatus) {
      throw new Error('Status or action is required')
    }

    console.log(`🔧 Admin Orders API: Updating order ${orderId} status to ${targetStatus}`)

    // Pass admin ID for audit trail
    await SupabaseAdminService.updateOrderStatus(orderId, targetStatus as any, user.id)

    console.log(`✅ Admin Orders API: Order status updated successfully`)

    const result = {
      success: true,
      message: 'Order status updated successfully',
      orderId,
      status: targetStatus
    }

    return NextResponse.json(result)
  } catch (error: any) {
    console.error('❌ Admin Orders API: Error updating order:', error)
    return NextResponse.json(
      { error: 'Failed to update order' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    console.log('🔧 Admin Orders API: Starting order delete request')

    // Check admin authentication using session-based approach
    const supabase = await createSupabaseServerClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      console.log('❌ Admin Orders API: No authenticated user found')
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Check if user is admin
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('role, status')
      .eq('id', user.id)
      .single()

    if (profileError || !profile || profile.role !== 'admin' || profile.status !== 'active') {
      console.log('❌ Admin Orders API: User is not an active admin:', { 
        role: profile?.role, 
        status: profile?.status 
      })
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      )
    }

    console.log('✅ Admin Orders API: Admin authentication successful for user:', user.id)

    // Try to get orderId from both query params and request body
    const { searchParams } = new URL(request.url)
    let orderId = searchParams.get('orderId')

    if (!orderId) {
      const body = await request.json()
      orderId = body.orderId
    }

    if (!orderId) {
      return NextResponse.json(
        { error: 'Order ID is required' },
        { status: 400 }
      )
    }

    console.log(`🔧 Admin Orders API: Deleting order ${orderId}`)

    await SupabaseAdminService.deleteOrder(orderId)

    console.log('✅ Admin Orders API: Order deleted successfully')

    return NextResponse.json({ success: true })
  } catch (error: any) {
    console.error('❌ Admin Orders API: Error deleting order:', error)
    return NextResponse.json(
      { error: 'Failed to delete order' },
      { status: 500 }
    )
  }
}
