import { NextRequest, NextResponse } from 'next/server'
import { DatabaseManager } from '../../../../../lib/services/database-manager'

// POST - Execute SQL query
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { query } = body

    if (!query) {
      return NextResponse.json(
        { error: 'SQL query is required' },
        { status: 400 }
      )
    }

    const result = await DatabaseManager.executeQuery(query)

    return NextResponse.json(result)
  } catch (error: any) {
    // Only log errors in development
    if (process.env.NODE_ENV === 'development') {
      console.error('❌ Admin API: Error executing query:', error)
    }
    return NextResponse.json(
      { error: error.message || 'Failed to execute query' },
      { status: 500 }
    )
  }
}
