import { NextResponse } from 'next/server'

/**
 * Health Check API Route
 * Simple endpoint to verify deployment is working
 */
export async function GET() {
  return NextResponse.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV,
    vercel: process.env.VERCEL ? 'true' : 'false',
    supabase: {
      url: process.env.NEXT_PUBLIC_SUPABASE_URL ? 'configured' : 'missing',
      serviceKey: process.env.SUPABASE_SERVICE_ROLE_KEY ? 'configured' : 'missing'
    }
  }, {
    headers: {
      'Cache-Control': 'no-cache',
      'Content-Type': 'application/json',
    },
  })
}
