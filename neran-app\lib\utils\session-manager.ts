/**
 * Session management utility with fixed 30-minute timeout
 */
import React from 'react'

export class SessionManager {
  private static instance: SessionManager
  private timeoutId: NodeJS.Timeout | null = null
  private readonly sessionTimeoutMinutes: number = 30 // Fixed timeout
  private onSessionExpired?: () => void
  private lastActivityTime: number = Date.now()

  private constructor() {
    this.setupActivityListeners()
  }

  public static getInstance(): SessionManager {
    if (!SessionManager.instance) {
      SessionManager.instance = new SessionManager()
    }
    return SessionManager.instance
  }

  /**
   * Initialize session manager
   */
  public initialize(onSessionExpired?: () => void): void {
    this.onSessionExpired = onSessionExpired
    this.resetTimeout()
  }

  /**
   * Reset the session timeout
   */
  public resetTimeout(): void {
    this.lastActivityTime = Date.now()
    
    if (this.timeoutId) {
      clearTimeout(this.timeoutId)
    }

    const timeoutMs = this.sessionTimeoutMinutes * 60 * 1000
    this.timeoutId = setTimeout(() => {
      this.handleSessionExpired()
    }, timeoutMs)
  }

  /**
   * Get remaining session time in minutes
   */
  public getRemainingTime(): number {
    const elapsed = Date.now() - this.lastActivityTime
    const remaining = (this.sessionTimeoutMinutes * 60 * 1000) - elapsed
    return Math.max(0, Math.floor(remaining / (60 * 1000)))
  }

  /**
   * Check if session is about to expire (within 5 minutes)
   */
  public isAboutToExpire(): boolean {
    return this.getRemainingTime() <= 5
  }

  /**
   * Manually expire the session
   */
  public expireSession(): void {
    this.handleSessionExpired()
  }

  /**
   * Clear the session timeout
   */
  public clearTimeout(): void {
    if (this.timeoutId) {
      clearTimeout(this.timeoutId)
      this.timeoutId = null
    }
  }

  /**
   * Setup activity listeners to reset timeout on user activity
   */
  private setupActivityListeners(): void {
    if (typeof window === 'undefined') return

    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click']
    
    const resetOnActivity = () => {
      this.resetTimeout()
    }

    events.forEach(event => {
      document.addEventListener(event, resetOnActivity, true)
    })
  }

  /**
   * Handle session expiration
   */
  private handleSessionExpired(): void {
    if (this.onSessionExpired) {
      this.onSessionExpired()
    } else {
      // Default behavior: show alert and reload
      alert('انتهت صلاحية جلستك. سيتم إعادة تحميل الصفحة.')
      window.location.reload()
    }
  }
}

/**
 * Hook for using session manager in React components
 * Only available in client-side React components
 */
export function useSessionManager(onSessionExpired?: () => void) {
  if (typeof window === 'undefined') {
    // Return dummy functions for SSR
    return {
      resetTimeout: () => {},
      getRemainingTime: () => 0,
      isAboutToExpire: () => false,
      expireSession: () => {},
    }
  }

  const sessionManager = SessionManager.getInstance()

  React.useEffect(() => {
    sessionManager.initialize(onSessionExpired)

    return () => {
      sessionManager.clearTimeout()
    }
  }, [onSessionExpired])

  return {
    resetTimeout: () => sessionManager.resetTimeout(),
    getRemainingTime: () => sessionManager.getRemainingTime(),
    isAboutToExpire: () => sessionManager.isAboutToExpire(),
    expireSession: () => sessionManager.expireSession(),
  }
}

// For non-React usage
export default SessionManager
