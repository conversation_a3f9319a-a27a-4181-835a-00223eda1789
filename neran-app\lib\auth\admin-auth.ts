/**
 * Admin Authentication Helper
 * Provides server-side admin authentication for API routes
 */

import { NextRequest } from 'next/server'
import { supabaseAdmin } from '../supabase-server'

export interface AdminAuthResult {
  isAdmin: boolean
  user?: any
  error?: string
}

export interface UserAuthResult {
  isAuthenticated: boolean
  user?: {
    id: string
    email: string
    role: 'user' | 'admin' | 'distributor'
    status: 'active' | 'pending' | 'suspended'
  }
  error?: string
}

/**
 * Verify if the current request is from an authenticated admin user
 */
export async function verifyAdminAuth(request: NextRequest): Promise<AdminAuthResult> {
  try {
    // Get the authorization header
    const authHeader = request.headers.get('authorization')
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return {
        isAdmin: false,
        error: 'No authorization token provided'
      }
    }

    const token = authHeader.replace('Bearer ', '')

    // Verify the JWT token with Supabase
    const { data: { user }, error } = await supabaseAdmin.auth.getUser(token)

    if (error || !user) {
      return {
        isAdmin: false,
        error: 'Invalid or expired token'
      }
    }

    // Get user profile to check role
    const { data: profile, error: profileError } = await supabaseAdmin
      .from('user_profiles')
      .select('role, status')
      .eq('id', user.id)
      .single()

    if (profileError || !profile) {
      return {
        isAdmin: false,
        error: 'User profile not found'
      }
    }

    // Check if user is admin and active
    if (profile.role !== 'admin' || profile.status !== 'active') {
      return {
        isAdmin: false,
        error: 'Insufficient permissions'
      }
    }

    return {
      isAdmin: true,
      user: {
        id: user.id,
        email: user.email,
        role: profile.role,
        status: profile.status
      }
    }
  } catch (error) {
    console.error('❌ Admin auth verification error:', error)
    return {
      isAdmin: false,
      error: 'Authentication verification failed'
    }
  }
}

/**
 * Get authenticated user information including role (for role-based pricing)
 */
export async function verifyUserAuth(request: NextRequest): Promise<UserAuthResult> {
  try {
    // Get the authorization header
    const authHeader = request.headers.get('authorization')
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return {
        isAuthenticated: false,
        error: 'No authorization token provided'
      }
    }

    const token = authHeader.replace('Bearer ', '')

    // Verify the JWT token with Supabase
    const { data: { user }, error } = await supabaseAdmin.auth.getUser(token)

    if (error || !user) {
      return {
        isAuthenticated: false,
        error: 'Invalid or expired token'
      }
    }

    // Get user profile to check role
    const { data: profile, error: profileError } = await supabaseAdmin
      .from('user_profiles')
      .select('role, status')
      .eq('id', user.id)
      .single()

    if (profileError || !profile) {
      return {
        isAuthenticated: false,
        error: 'User profile not found'
      }
    }

    // Any active user can be authenticated (admin, user, or distributor)
    if (profile.status !== 'active') {
      return {
        isAuthenticated: false,
        error: 'Account not active'
      }
    }

    return {
      isAuthenticated: true,
      user: {
        id: user.id,
        email: user.email || '',
        role: profile.role as 'user' | 'admin' | 'distributor',
        status: profile.status as 'active' | 'pending' | 'suspended'
      }
    }
  } catch (error) {
    console.error('❌ User auth verification error:', error)
    return {
      isAuthenticated: false,
      error: 'Authentication verification failed'
    }
  }
}

/**
 * Middleware wrapper for admin-only API routes
 */
export function withAdminAuth(handler: (request: NextRequest, context: any) => Promise<Response>) {
  return async (request: NextRequest, context: any) => {
    const authResult = await verifyAdminAuth(request)
    
    if (!authResult.isAdmin) {
      return new Response(
        JSON.stringify({ 
          error: authResult.error || 'Admin access required',
          code: 'ADMIN_ACCESS_REQUIRED'
        }),
        { 
          status: 403,
          headers: { 'Content-Type': 'application/json' }
        }
      )
    }

    // Add user info to request context
    ;(request as any).adminUser = authResult.user

    return handler(request, context)
  }
}

/**
 * Simple admin check for API routes (without token verification)
 * Use this for routes that don't require strict authentication
 */
export async function isAdminUser(userId: string): Promise<boolean> {
  try {
    const { data: profile, error } = await supabaseAdmin
      .from('user_profiles')
      .select('role, status')
      .eq('id', userId)
      .single()

    if (error || !profile) {
      return false
    }

    return profile.role === 'admin' && profile.status === 'active'
  } catch (error) {
    console.error('❌ Admin check error:', error)
    return false
  }
}
