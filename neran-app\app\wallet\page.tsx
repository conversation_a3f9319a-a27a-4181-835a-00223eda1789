"use client"

import { useState, useEffect, useRef } from "react"
import { Wallet, ArrowUpRight, ArrowDownLeft, ChevronLeft, ChevronRight, MessageCircle, RefreshCw } from "lucide-react"
import { useSupabaseAuth } from "../../contexts/SupabaseAuthContext"
import CopyButton from "../components/CopyButton"
import { Transaction } from "../../types"
import AccountStatusGuard from "../../components/AccountStatusGuard"
import { formatDateTime } from "../../lib/utils/date-utils"
import { useUserTransactionsPaginated } from "../../hooks/useTransactions"
import { useToast } from "../../hooks/use-toast"

// Helper function to format dates using centralized utility
const formatFirebaseDateTime = (dateString: string, fallback: string = 'غير محدد'): string => {
  try {
    return formatDateTime(dateString)
  } catch (error) {
    return fallback
  }
}

function WalletPage() {
  const { user, refreshUserData } = useSupabaseAuth()
  const { toast } = useToast()

  // ✅ PERFORMANCE: Server-side pagination state
  const [currentPage, setCurrentPage] = useState(1)
  const transactionsPerPage = 5 // Increased from 3 to 5 since we're not loading all transactions

  // ✅ PERFORMANCE: Use paginated hook instead of loading all transactions
  const { 
    data: paginatedData = { transactions: [], totalCount: 0, totalPages: 0 },
    isLoading: loading, 
    error: queryError,
    refetch,
    isFetching
  } = useUserTransactionsPaginated(user?.id || '', currentPage, transactionsPerPage)

  const [error, setError] = useState<string | null>(null)
  const [whatsAppNumber, setWhatsAppNumber] = useState<string>("+966501234567")
  const [isRefreshing, setIsRefreshing] = useState(false)
  
  // ✅ RATE LIMITING: Add cooldown mechanism for refresh button
  const [lastRefreshTime, setLastRefreshTime] = useState<number>(0)
  const [refreshCooldown, setRefreshCooldown] = useState<number>(0)
  const REFRESH_COOLDOWN_MS = 3000 // 3 seconds cooldown

  // ✅ Cleanup timeout ref
  const balanceRefreshTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // ✅ RATE LIMITING: Update cooldown timer
  useEffect(() => {
    if (refreshCooldown > 0) {
      const timer = setInterval(() => {
        setRefreshCooldown(prev => Math.max(0, prev - 100))
      }, 100)
      return () => clearInterval(timer)
    }
  }, [refreshCooldown])

  // ✅ REMOVED: Automatic balance refresh to prevent rate limiting
  // Balance will only be refreshed manually via the refresh button
  // This eliminates the infinite loop of authentication requests
  useEffect(() => {
    // Cleanup timeout on unmount
    return () => {
      if (balanceRefreshTimeoutRef.current) {
        clearTimeout(balanceRefreshTimeoutRef.current)
      }
    }
  }, [])

  // Handle query error
  useEffect(() => {
    if (queryError) {
      setError('حدث خطأ أثناء جلب المعاملات')
    } else {
      setError(null)
    }
  }, [queryError])

  // ✅ PERFORMANCE: Format only the current page transactions (not all)
  const transactions = paginatedData.transactions.map((transaction: Transaction) => {
    const formattedDate = formatFirebaseDateTime(transaction.createdAt, 'غير محدد')

    // Determine transaction status based on order status
    let status = 'مكتملة' // Default for non-order transactions
    if (transaction.orderId && transaction.orderStatus) {
      switch (transaction.orderStatus) {
        case 'pending':
          status = 'قيد المراجعة'
          break
        case 'processing':
          status = 'قيد المعالجة'
          break
        case 'completed':
          status = 'مكتملة'
          break
        case 'cancelled':
          status = 'ملغية'
          break
        default:
          status = 'مكتملة'
      }
    }

    return {
      title: transaction.description || 'معاملة مالية',
      amount: transaction.type === 'credit' ? transaction.amount : -transaction.amount,
      date: formattedDate,
      type: transaction.type,
      status: status,
      orderId: transaction.orderId,
      orderStatus: transaction.orderStatus
    }
  })

  // ✅ PERFORMANCE: Use server-side pagination data
  const { totalCount, totalPages } = paginatedData

  // Pagination handlers
  const goToNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1)
    }
  }

  const goToPrevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1)
    }
  }

  const goToPage = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page)
    }
  }

  // ✅ ENHANCED: Manual refresh handler with rate limiting and balance update
  const handleManualRefresh = async () => {
    const now = Date.now()
    
    // ✅ RATE LIMITING: Check if still in cooldown period (but allow first time)
    if (lastRefreshTime > 0 && now - lastRefreshTime < REFRESH_COOLDOWN_MS) {
      const remainingTime = REFRESH_COOLDOWN_MS - (now - lastRefreshTime)
      const remainingSeconds = Math.ceil(remainingTime / 1000)
      console.log(`⏳ Refresh blocked - ${remainingSeconds}s cooldown remaining`)
      
      // Show toast notification to user
      toast({
        title: "⏳ يرجى الانتظار",
        description: `يمكنك تحديث البيانات مرة أخرى خلال ${remainingSeconds} ثانية`,
        variant: "default",
      })
      
      return
    }

    // ✅ RATE LIMITING: Set cooldown timer
    setLastRefreshTime(now)
    setRefreshCooldown(REFRESH_COOLDOWN_MS)
    
    setIsRefreshing(true)
    try {
      console.log('🔄 Manual refresh triggered - refreshing transactions and balance')
      
      // Refresh both transactions and user balance
      await Promise.all([
        refetch(),
        refreshUserData()
      ])
      
      console.log('✅ Manual refresh completed')
      
      // Show success toast
      toast({
        title: "✅ تم التحديث بنجاح",
        description: "تم تحديث بيانات المحفظة والمعاملات",
        variant: "default",
      })
    } catch (error) {
      console.error('❌ Error during manual refresh:', error)
      
      // Show error toast
      toast({
        title: "❌ خطأ في التحديث",
        description: "حدث خطأ أثناء تحديث البيانات، يرجى المحاولة مرة أخرى",
        variant: "destructive",
      })
    } finally {
      setIsRefreshing(false)
    }
  }

  useEffect(() => {
    // Fetch WhatsApp number from settings API
    const fetchWhatsAppNumber = async () => {
      try {
        const response = await fetch('/api/settings')
        if (response.ok) {
          const settings = await response.json()
          if (settings && settings.supportWhatsApp) {
            setWhatsAppNumber(settings.supportWhatsApp)
          }
        }
      } catch (error) {
        console.warn('Error fetching WhatsApp number from settings:', error)
        // Keep default number if fetch fails
      }
    }

    fetchWhatsAppNumber()
  }, [])

  if (!user) {
    return (
      <div className="container mx-auto px-4 py-8 max-w-4xl min-h-screen">
        <div className="text-center text-white">جاري التحميل...</div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900">
      <div className="container mx-auto px-4 py-6 max-w-6xl">
        {/* Modern Header */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-red-500 to-red-600 rounded-2xl mb-4 shadow-lg">
            <Wallet className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-2xl md:text-3xl font-bold text-white mb-2">محفظتي الرقمية</h1>
          <p className="text-gray-400 text-sm md:text-base">إدارة رصيدك ومعاملاتك بسهولة</p>
        </div>

        {/* Mobile-First Layout */}
        <div className="space-y-6">
          {/* Modern Balance Card */}
          <div className="relative overflow-hidden bg-gradient-to-br from-red-500 via-red-600 to-red-700 rounded-3xl p-6 md:p-8 shadow-2xl">
            {/* Background Pattern */}
            <div className="absolute inset-0 bg-black/10"></div>
            <div className="absolute -top-4 -right-4 w-24 h-24 bg-white/10 rounded-full blur-xl"></div>
            <div className="absolute -bottom-4 -left-4 w-32 h-32 bg-white/5 rounded-full blur-xl"></div>

            <div className="relative z-10">
              {/* Header */}
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 bg-white/20 rounded-2xl flex items-center justify-center backdrop-blur-sm">
                    <Wallet className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h2 className="text-lg md:text-xl font-bold text-white">الرصيد المتاح</h2>
                    <p className="text-white/80 text-sm">جاهز للاستخدام</p>
                  </div>
                </div>

                {/* User ID - Mobile Optimized */}
                <div className="text-right">
                  <div className="text-xs text-white/70 mb-1">معرف المستخدم</div>
                  <div className="text-xs md:text-sm font-mono flex items-center gap-1 bg-white/10 px-2 py-1 rounded-lg backdrop-blur-sm">
                    <span className="truncate max-w-[80px] md:max-w-none">{user.id}</span>
                    <CopyButton text={user.id} size="sm" className="text-white/80 hover:text-white flex-shrink-0" />
                  </div>
                </div>
              </div>

              {/* Balance Display */}
              <div className="text-center md:text-left">
                <div className="text-4xl md:text-5xl font-bold text-white mb-4 tracking-tight">
                  ${(typeof user.balance === 'number' ? user.balance : 0).toFixed(2)}
                </div>
                <div className="flex items-center justify-center md:justify-start gap-2 text-white/80 text-sm mb-6">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span>محدث الآن</span>
                  {(isFetching || isRefreshing) && (
                    <>
                      <span>•</span>
                      <div className="flex items-center gap-1">
                        <RefreshCw className="w-3 h-3 animate-spin" />
                        <span className="text-xs">جاري التحديث</span>
                      </div>
                    </>
                  )}
                </div>

                {/* WhatsApp Support Button */}
                <button
                  onClick={() => {
                    const message = `🔥 *طلب شحن رصيد - نيران كارد*\n\n` +
                      `👤 *معرف المستخدم:* ${user.id}\n` +
                      `💰 *الرصيد الحالي:* $${(typeof user.balance === 'number' ? user.balance : 0).toFixed(2)}\n\n` +
                      `مرحباً، أريد شحن رصيد محفظتي الرقمية.\n` +
                      `يرجى إرشادي لطرق الدفع المتاحة وإتمام عملية الشحن.\n\n` +
                      `شكراً لكم 🙏`

                    // Remove + from the number if it exists and format for WhatsApp
                    const phoneNumber = whatsAppNumber.replace(/\+/g, '')
                    const whatsappUrl = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`
                    window.open(whatsappUrl, '_blank')
                  }}
                  className="w-full md:w-auto bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white py-3 px-6 rounded-xl transition-all duration-200 flex items-center justify-center gap-3 shadow-lg hover:shadow-xl transform hover:scale-[1.02]"
                >
                  <MessageCircle className="w-5 h-5" />
                  شحن الرصيد
                </button>
              </div>
            </div>
          </div>

          {/* Modern Transaction History */}
          <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6 shadow-xl">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-blue-500/20 rounded-xl flex items-center justify-center">
                  <ArrowUpRight className="w-5 h-5 text-blue-400" />
                </div>
                <div>
                  <h2 className="text-xl font-bold text-white">سجل المعاملات</h2>
                  <p className="text-gray-400 text-sm">آخر العمليات المالية</p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                {/* ✅ ENHANCED: Manual refresh button with rate limiting and balance update */}
                <div className="relative">
                  <button
                    onClick={handleManualRefresh}
                    disabled={isFetching || isRefreshing || refreshCooldown > 0}
                    className="flex items-center gap-2 bg-white/10 hover:bg-white/20 border border-white/20 px-3 py-2 rounded-lg text-white text-sm transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                    title={
                      refreshCooldown > 0 
                        ? `انتظر ${Math.ceil(refreshCooldown / 1000)} ثانية قبل التحديث مرة أخرى`
                        : "تحديث المعاملات والرصيد"
                    }
                  >
                    <RefreshCw className={`w-4 h-4 ${(isFetching || isRefreshing) ? 'animate-spin' : ''}`} />
                    <span className="hidden md:inline">
                      {isRefreshing 
                        ? 'جاري التحديث...' 
                        : refreshCooldown > 0 
                          ? `انتظر ${Math.ceil(refreshCooldown / 1000)}ث`
                          : 'تحديث'
                      }
                    </span>
                  </button>
                  
                  {/* Cooldown Progress Indicator */}
                  {refreshCooldown > 0 && (
                    <div className="absolute -top-1 -right-1 w-6 h-6 rounded-full bg-yellow-500/20 border-2 border-yellow-500 flex items-center justify-center">
                      <div 
                        className="w-3 h-3 rounded-full bg-yellow-500 animate-pulse"
                        style={{
                          animationDuration: `${refreshCooldown}ms`,
                          animationTimingFunction: 'linear'
                        }}
                      ></div>
                    </div>
                  )}
                </div>

                <div className="text-sm text-gray-400 flex items-center gap-2">
                  {transactions.length > 0 && (
                    <div className="bg-white/10 px-3 py-1 rounded-lg backdrop-blur-sm">
                      <span className="text-white font-semibold">{totalCount}</span>
                      <span className="text-gray-400 mr-1">معاملة</span>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {loading ? (
              <div className="text-center py-12">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-500/20 rounded-2xl mb-4">
                  <div className="w-8 h-8 border-4 border-blue-400 border-t-transparent rounded-full animate-spin"></div>
                </div>
                <div className="text-gray-300 font-medium">جاري تحميل المعاملات...</div>
                <div className="text-gray-500 text-sm mt-1">تحسين الأداء مع التخزين المؤقت</div>
              </div>
            ) : error ? (
              <div className="text-center py-12">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-red-500/20 rounded-2xl mb-4">
                  <ArrowUpRight className="w-8 h-8 text-red-400" />
                </div>
                <div className="text-red-400 mb-4 font-medium">{error}</div>
                <button
                  onClick={handleManualRefresh}
                  disabled={isRefreshing || refreshCooldown > 0}
                  className="bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-xl text-sm font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
                  title={
                    refreshCooldown > 0 
                      ? `انتظر ${Math.ceil(refreshCooldown / 1000)} ثانية قبل المحاولة مرة أخرى`
                      : "إعادة المحاولة"
                  }
                >
                  {isRefreshing && <RefreshCw className="w-4 h-4 animate-spin" />}
                  {isRefreshing 
                    ? 'جاري إعادة المحاولة...' 
                    : refreshCooldown > 0 
                      ? `انتظر ${Math.ceil(refreshCooldown / 1000)} ثانية`
                      : 'إعادة المحاولة'
                  }
                </button>
              </div>
            ) : transactions.length === 0 ? (
              <div className="text-center py-12">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-gray-500/20 rounded-2xl mb-4">
                  <Wallet className="w-8 h-8 text-gray-400" />
                </div>
                <div className="text-gray-300 mb-2 font-medium">لا توجد معاملات حتى الآن</div>
                <div className="text-gray-500 text-sm max-w-sm mx-auto">ستظهر معاملاتك هنا بعد إجراء أول عملية شراء أو شحن</div>
              </div>
            ) : (
              <>
                {/* Modern Transaction Cards */}
                <div className="space-y-3">
                  {transactions.map((transaction, index) => (
                    <div key={index} className="group bg-white/5 hover:bg-white/10 border border-white/10 hover:border-white/20 rounded-2xl p-4 transition-all duration-200 hover:shadow-lg">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4 flex-1 min-w-0">
                          <div
                            className={`w-12 h-12 rounded-2xl flex items-center justify-center flex-shrink-0 ${
                              transaction.type === "debit"
                                ? "bg-red-500/20 group-hover:bg-red-500/30"
                                : transaction.type === "credit"
                                  ? "bg-green-500/20 group-hover:bg-green-500/30"
                                  : "bg-blue-500/20 group-hover:bg-blue-500/30"
                            }`}
                          >
                            {transaction.type === "debit" ? (
                              <ArrowUpRight className="w-6 h-6 text-red-400" />
                            ) : transaction.type === "credit" ? (
                              <ArrowDownLeft className="w-6 h-6 text-green-400" />
                            ) : (
                              <ArrowDownLeft className="w-6 h-6 text-blue-400" />
                            )}
                          </div>
                          <div className="flex-1 min-w-0">
                            <h3 className="text-white font-semibold text-sm md:text-base truncate">{transaction.title}</h3>
                            <p className="text-gray-400 text-xs md:text-sm">{transaction.date}</p>
                            {transaction.orderId && (
                              <p className="text-gray-500 text-xs mt-1">طلب #{transaction.orderId.slice(-8)}</p>
                            )}
                          </div>
                        </div>
                        
                        <div className="text-right flex-shrink-0">
                          <div className={`font-bold text-lg ${transaction.amount > 0 ? "text-green-400" : "text-red-400"}`}>
                            {transaction.amount > 0 ? "+" : ""}${Math.abs(transaction.amount).toFixed(2)}
                          </div>
                          <div className={`text-xs px-2 py-1 rounded-lg mt-1 ${
                            transaction.status === 'مكتملة' ? 'bg-green-500/20 text-green-400' :
                            transaction.status === 'قيد المراجعة' ? 'bg-yellow-500/20 text-yellow-400' :
                            transaction.status === 'قيد المعالجة' ? 'bg-blue-500/20 text-blue-400' :
                            transaction.status === 'ملغية' ? 'bg-red-500/20 text-red-400' :
                            'bg-white/5 text-gray-400'
                          }`}>
                            {transaction.status}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Modern Pagination */}
                {totalPages > 1 && (
                  <div className="mt-8 pt-6 border-t border-white/10">
                    {/* Mobile Pagination */}
                    <div className="flex flex-col gap-4 md:hidden">
                      <div className="text-center text-sm text-gray-400">
                        صفحة {currentPage} من {totalPages} • {totalCount} معاملة
                      </div>
                      <div className="flex items-center justify-center gap-2">
                        <button
                          onClick={goToPrevPage}
                          disabled={currentPage === 1}
                          className="w-10 h-10 rounded-xl bg-white/5 hover:bg-white/10 border border-white/10 hover:border-white/20 flex items-center justify-center transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          <ChevronRight className="w-5 h-5 text-gray-400" />
                        </button>

                        <div className="flex items-center gap-1">
                          {Array.from({ length: Math.min(totalPages, 5) }, (_, i) => {
                            let page;
                            if (totalPages <= 5) {
                              page = i + 1;
                            } else if (currentPage <= 3) {
                              page = i + 1;
                            } else if (currentPage >= totalPages - 2) {
                              page = totalPages - 4 + i;
                            } else {
                              page = currentPage - 2 + i;
                            }

                            return (
                              <button
                                key={page}
                                onClick={() => goToPage(page)}
                                className={`w-10 h-10 rounded-xl flex items-center justify-center text-sm font-medium transition-all duration-200 ${
                                  currentPage === page
                                    ? "bg-red-500 text-white shadow-lg"
                                    : "bg-white/5 hover:bg-white/10 border border-white/10 hover:border-white/20 text-gray-400 hover:text-white"
                                }`}
                              >
                                {page}
                              </button>
                            );
                          })}
                        </div>

                        <button
                          onClick={goToNextPage}
                          disabled={currentPage === totalPages}
                          className="w-10 h-10 rounded-xl bg-white/5 hover:bg-white/10 border border-white/10 hover:border-white/20 flex items-center justify-center transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          <ChevronLeft className="w-5 h-5 text-gray-400" />
                        </button>
                      </div>
                    </div>

                    {/* Desktop Pagination */}
                    <div className="hidden md:flex items-center justify-between">
                      <div className="text-sm text-gray-400">
                        صفحة {currentPage} من {totalPages} • {totalCount} معاملة
                      </div>

                      <div className="flex items-center gap-2">
                        <button
                          onClick={goToPrevPage}
                          disabled={currentPage === 1}
                          className="w-10 h-10 rounded-xl bg-white/5 hover:bg-white/10 border border-white/10 hover:border-white/20 flex items-center justify-center transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          <ChevronRight className="w-5 h-5 text-gray-400" />
                        </button>

                        <div className="flex items-center gap-1">
                          {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                            <button
                              key={page}
                              onClick={() => goToPage(page)}
                              className={`w-10 h-10 rounded-xl flex items-center justify-center text-sm font-medium transition-all duration-200 ${
                                currentPage === page
                                  ? "bg-red-500 text-white shadow-lg"
                                  : "bg-white/5 hover:bg-white/10 border border-white/10 hover:border-white/20 text-gray-400 hover:text-white"
                              }`}
                            >
                              {page}
                            </button>
                          ))}
                        </div>

                        <button
                          onClick={goToNextPage}
                          disabled={currentPage === totalPages}
                          className="w-10 h-10 rounded-xl bg-white/5 hover:bg-white/10 border border-white/10 hover:border-white/20 flex items-center justify-center transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          <ChevronLeft className="w-5 h-5 text-gray-400" />
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

// Wrap with authentication and status guard
export default function ProtectedWalletPage() {
  return (
    <AccountStatusGuard>
      <WalletPage />
    </AccountStatusGuard>
  )
}
