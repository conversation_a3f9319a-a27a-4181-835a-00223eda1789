/**
 * Supabase Client Configuration
 * Replaces Firebase configuration with Supabase
 */

import { createClient } from '@supabase/supabase-js'
import { createBrowserClient } from '@supabase/ssr'

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

// Singleton instance to prevent multiple clients
let browserClientInstance: ReturnType<typeof createBrowserClient> | null = null

// Browser client for client-side operations (singleton)
export const createSupabaseBrowserClient = () => {
  if (typeof window === 'undefined') {
    // Server-side: create new instance
    return createBrowserClient(supabaseUrl, supabaseAnonKey, {
      realtime: {
        params: {
          eventsPerSecond: 2, // Further reduced to prevent rate limiting
        },
      },
      auth: {
        persistSession: true,
        autoRefreshToken: true, // Re-enable with better session handling
        detectSessionInUrl: true,
        flowType: 'pkce',
        debug: false // Disable debug logs
      },
      global: {
        headers: { 
          'x-client-info': 'neran-card-browser',
          'x-client-version': '1.0.0'
        },
      },
    })
  }

  // Client-side: use singleton
  if (!browserClientInstance) {
    browserClientInstance = createBrowserClient(supabaseUrl, supabaseAnonKey, {
      realtime: {
        params: {
          eventsPerSecond: 2, // Further reduced to prevent rate limiting
        },
      },
      auth: {
        persistSession: true,
        autoRefreshToken: true, // Re-enable with better session handling
        detectSessionInUrl: true,
        flowType: 'pkce',
        debug: false // Disable debug logs
      },
      global: {
        headers: { 
          'x-client-info': 'neran-card-browser',
          'x-client-version': '1.0.0'
        },
      },
    })
    
    if (process.env.NODE_ENV === 'development') {
      console.log('🔧 Created new Supabase browser client instance with rate limiting protection')
    }
  }

  return browserClientInstance
}

// Legacy client for backward compatibility (deprecated)
export const supabase = createSupabaseBrowserClient()

// Server-side Supabase client factory (for admin operations)
export const createSupabaseServerClient = () => {
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

  if (!supabaseServiceKey) {
    throw new Error('SUPABASE_SERVICE_ROLE_KEY is required for server-side operations')
  }

  return createClient(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    },
    realtime: {
      params: {
        eventsPerSecond: 3, // Reduced for server operations
      },
    },
    global: {
      headers: { 
        'x-client-info': 'neran-card-server',
        'x-client-version': '1.0.0'
      },
    }
  })
}

// Database types (will be generated from Supabase)
export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          name: string
          role: 'user' | 'admin'
          status: 'pending' | 'active' | 'suspended'
          balance: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          email: string
          name: string
          role?: 'user' | 'admin'
          status?: 'pending' | 'active' | 'suspended'
          balance?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          name?: string
          role?: 'user' | 'admin'
          status?: 'pending' | 'active' | 'suspended'
          balance?: number
          created_at?: string
          updated_at?: string
        }
      }
      products: {
        Row: {
          id: string
          name: string
          category: 'gems' | 'membership' | 'pass'
          price: number
          description: string | null
          is_active: boolean
          image_url: string | null
          game_type: string | null
          sort_order: number
          has_offer: boolean
          original_price: number | null
          discount_percentage: number | null
          offer_start_date: string | null
          offer_end_date: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          category: 'gems' | 'membership' | 'pass'
          price: number
          description?: string | null
          is_active?: boolean
          image_url?: string | null
          game_type?: string | null
          sort_order?: number
          has_offer?: boolean
          original_price?: number | null
          discount_percentage?: number | null
          offer_start_date?: string | null
          offer_end_date?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          category?: 'gems' | 'membership' | 'pass'
          price?: number
          description?: string | null
          is_active?: boolean
          image_url?: string | null
          game_type?: string | null
          sort_order?: number
          has_offer?: boolean
          original_price?: number | null
          discount_percentage?: number | null
          offer_start_date?: string | null
          offer_end_date?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      orders: {
        Row: {
          id: string
          user_id: string
          product_id: string
          game_id: string
          amount: string
          price: number
          status: 'pending' | 'processing' | 'completed' | 'cancelled'
          notes: string | null
          admin_notes: string | null
          created_at: string
          updated_at: string
          completed_at: string | null
        }
        Insert: {
          id?: string
          user_id: string
          product_id: string
          game_id: string
          amount: string
          price: number
          status?: 'pending' | 'processing' | 'completed' | 'cancelled'
          notes?: string | null
          admin_notes?: string | null
          created_at?: string
          updated_at?: string
          completed_at?: string | null
        }
        Update: {
          id?: string
          user_id?: string
          product_id?: string
          game_id?: string
          amount?: string
          price?: number
          status?: 'pending' | 'processing' | 'completed' | 'cancelled'
          notes?: string | null
          admin_notes?: string | null
          created_at?: string
          updated_at?: string
          completed_at?: string | null
        }
      }
      transactions: {
        Row: {
          id: string
          user_id: string
          order_id: string | null
          type: 'deposit' | 'purchase' | 'refund' | 'admin_adjustment'
          amount: number
          description: string | null
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          order_id?: string | null
          type: 'deposit' | 'purchase' | 'refund' | 'admin_adjustment'
          amount: number
          description?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          order_id?: string | null
          type?: 'deposit' | 'purchase' | 'refund' | 'admin_adjustment'
          amount?: number
          description?: string | null
          created_at?: string
        }
      }
      system_settings: {
        Row: {
          key: string
          value: any
          description: string | null
          updated_at: string
        }
        Insert: {
          key: string
          value: any
          description?: string | null
          updated_at?: string
        }
        Update: {
          key?: string
          value?: any
          description?: string | null
          updated_at?: string
        }
      }
    }
    Views: {
      orders_with_details: {
        Row: {
          id: string
          user_id: string
          product_id: string
          game_id: string
          amount: string
          price: number
          status: 'pending' | 'processing' | 'completed' | 'cancelled'
          notes: string | null
          admin_notes: string | null
          created_at: string
          updated_at: string
          completed_at: string | null
          user_name: string
          user_email: string
          user_role: 'user' | 'admin'
          product_name: string
          product_category: 'gems' | 'membership' | 'pass'
        }
      }
      dashboard_stats: {
        Row: {
          total_users: number
          active_users: number
          pending_users: number
          admin_users: number
          total_orders: number
          pending_orders: number
          completed_orders: number
          cancelled_orders: number
          total_revenue: number
          active_products: number
        }
      }
    }
  }
}

export type SupabaseClient = ReturnType<typeof createSupabaseBrowserClient>
