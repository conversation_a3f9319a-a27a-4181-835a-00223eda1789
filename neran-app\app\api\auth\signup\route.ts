import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '../../../../lib/supabase'
import { supabaseAdmin } from '../../../../lib/supabase-admin'
import { SupabaseAdminService } from '../../../../lib/services/supabase-admin'

/**
 * User Signup API Route
 * Handles user registration with proper server-side validation
 */
export async function POST(request: NextRequest) {
  try {
    const { email, password, name } = await request.json()

    if (!email || !password || !name) {
      return NextResponse.json(
        { error: 'Email, password, and name are required' },
        { status: 400 }
      )
    }

    console.log('🔐 Starting signup process for:', email)

    // Check if user already exists in our database
    const { data: existingProfile } = await supabaseAdmin
      .from('user_profiles')
      .select('id, email, status')
      .eq('email', email)
      .single()

    if (existingProfile) {
      console.log('⚠️ User with this email already exists')
      return NextResponse.json(
        { error: 'يوجد حساب مسجل بهذا البريد الإلكتروني بالفعل' },
        { status: 400 }
      )
    }

    // Check if registrations are allowed (server-side)
    const settings = await SupabaseAdminService.getSystemSettings()
    if (!settings.allowRegistrations) {
      console.log('❌ Registration attempt blocked - registrations disabled')
      return NextResponse.json(
        { error: 'التسجيل غير متاح حالياً. يرجى المحاولة لاحقاً أو التواصل مع الدعم الفني.' },
        { status: 403 }
      )
    }

    // Sign up with Supabase Auth
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          name: name,
          role: 'user'
        }
      }
    })

    if (authError) {
      console.error('❌ Supabase Auth signup error:', authError)
      return NextResponse.json(
        { error: authError.message },
        { status: 400 }
      )
    }

    if (!authData.user) {
      return NextResponse.json(
        { error: 'No user data returned from signup' },
        { status: 400 }
      )
    }

    // Determine user status based on settings
    const userStatus = settings.autoApproveUsers ? 'active' : 'pending'
    console.log(`👤 User will be created with status: ${userStatus} (autoApprove: ${settings.autoApproveUsers})`)

    // Create user record in our user_profiles table using admin client to bypass RLS
    const { data: userData, error: dbError } = await supabaseAdmin
      .from('user_profiles')
      .insert({
        id: authData.user.id,
        email: email,
        name: name,
        role: 'user',
        status: userStatus,
        balance: 0
      })
      .select()
      .single()

    if (dbError) {
      console.error('❌ Database user creation error:', dbError)

      // If it's a duplicate key error, the user profile already exists
      if (dbError.code === '23505') {
        console.log('⚠️ User profile already exists, attempting to fetch existing profile')

        // Try to get the existing user profile
        const { data: existingUser, error: fetchError } = await supabaseAdmin
          .from('user_profiles')
          .select('*')
          .eq('id', authData.user.id)
          .single()

        if (!fetchError && existingUser) {
          console.log('✅ Found existing user profile, returning it')
          return NextResponse.json({
            success: true,
            user: {
              id: existingUser.id,
              email: existingUser.email,
              name: existingUser.name,
              role: existingUser.role,
              status: existingUser.status,
              balance: existingUser.balance
            },
            message: existingUser.status === 'pending'
              ? 'تم إنشاء الحساب بنجاح. في انتظار موافقة الإدارة.'
              : 'تم إنشاء الحساب بنجاح.'
          })
        }
      }

      // For other database errors, clean up the auth user and return error
      try {
        console.log('🧹 Cleaning up auth user due to profile creation failure')
        await supabaseAdmin.auth.admin.deleteUser(authData.user.id)
      } catch (cleanupError) {
        console.warn('⚠️ Failed to cleanup auth user:', cleanupError)
      }

      return NextResponse.json(
        { error: 'Failed to create user profile' },
        { status: 500 }
      )
    }

    console.log('✅ Signup successful')
    
    return NextResponse.json({
      success: true,
      user: {
        id: userData.id,
        email: userData.email,
        name: userData.name,
        role: userData.role,
        status: userData.status,
        balance: userData.balance
      },
      message: userStatus === 'pending' 
        ? 'تم إنشاء الحساب بنجاح. في انتظار موافقة الإدارة.'
        : 'تم إنشاء الحساب بنجاح.'
    })

  } catch (error: any) {
    console.error('❌ Signup API error:', error)
    return NextResponse.json(
      { error: error.message || 'حدث خطأ أثناء إنشاء الحساب' },
      { status: 500 }
    )
  }
}
