import type React from "react"
import type { Metada<PERSON> } from "next"
import { Cairo } from "next/font/google"
import "./globals.css"
import Navbar from "./components/Navbar"
import { UserProvider } from "./context/UserContext"

const cairo = Cairo({
  subsets: ["arabic", "latin"],
  display: "swap",
})

export const metadata: Metadata = {
  title: "نيران كارد - شحن بطاقات الألعاب والتطبيقات | Neran Card",
  description:
    "نيران كارد - أفضل موقع لشحن بطاقات الألعاب والتطبيقات. شحن فوري وآمن لجميع الألعاب مثل PUBG, Free Fire, Fortnite وأكثر. أسعار منافسة وخدمة عملاء ممتازة.",
  keywords: [
    "نيران كارد",
    "neran card",
    "nerancard",
    "شحن ألعاب",
    "بطاقات ألعاب",
    "شحن PUBG",
    "شحن Free Fire",
    "شحن Fortnite",
    "جواهر PUBG",
    "عضويات ألعاب",
    "شحن فوري",
    "بطاقات تطبيقات",
    "شحن آمن",
    "gaming cards",
    "game top up",
    "mobile gaming",
    "gaming credits",
    "digital cards",
    "instant delivery",
    "gaming store",
    "online gaming",
    "mobile games",
    "gaming platform",
  ].join(", "),
  authors: [{ name: "Neran Card", url: "https://nerancard.com" }],
  creator: "Neran Card",
  publisher: "Neran Card",
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  openGraph: {
    type: "website",
    locale: "ar_SA",
    alternateLocale: ["en_US"],
    url: "https://nerancard.com",
    siteName: "نيران كارد - Neran Card",
    title: "نيران كارد - شحن بطاقات الألعاب والتطبيقات | Neran Card",
    description:
      "نيران كارد - أفضل موقع لشحن بطاقات الألعاب والتطبيقات. شحن فوري وآمن لجميع الألعاب مثل PUBG, Free Fire, Fortnite وأكثر.",
    images: [
      {
        url: "https://nerancard.com/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "نيران كارد - شحن بطاقات الألعاب",
        type: "image/jpeg",
      },
      {
        url: "https://nerancard.com/og-image-square.jpg",
        width: 1200,
        height: 1200,
        alt: "نيران كارد - شحن بطاقات الألعاب",
        type: "image/jpeg",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    site: "@nerancard",
    creator: "@nerancard",
    title: "نيران كارد - شحن بطاقات الألعاب والتطبيقات",
    description: "نيران كارد - أفضل موقع لشحن بطاقات الألعاب والتطبيقات. شحن فوري وآمن لجميع الألعاب.",
    images: ["https://nerancard.com/twitter-image.jpg"],
  },
  verification: {
    google: "your-google-verification-code",
    yandex: "your-yandex-verification-code",
    yahoo: "your-yahoo-verification-code",
  },
  alternates: {
    canonical: "https://nerancard.com",
    languages: {
      "ar-SA": "https://nerancard.com",
      "en-US": "https://nerancard.com/en",
    },
  },
  category: "Gaming",
  classification: "Gaming Cards and Digital Services",
  other: {
    "apple-mobile-web-app-title": "نيران كارد",
    "application-name": "Neran Card",
    "msapplication-TileColor": "#8B2635",
    "theme-color": "#8B2635",
  },
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="ar" dir="rtl" className="dark">
      <head>
        <link rel="canonical" href="https://nerancard.com" />
        <link rel="alternate" hrefLang="ar" href="https://nerancard.com" />
        <link rel="alternate" hrefLang="en" href="https://nerancard.com/en" />
        <link rel="alternate" hrefLang="x-default" href="https://nerancard.com" />

        {/* Structured Data */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "Organization",
              name: "نيران كارد",
              alternateName: "Neran Card",
              url: "https://nerancard.com",
              logo: "https://nerancard.com/logo.png",
              description: "أفضل موقع لشحن بطاقات الألعاب والتطبيقات في الشرق الأوسط",
              sameAs: [
                "https://twitter.com/nerancard",
                "https://facebook.com/nerancard",
                "https://instagram.com/nerancard",
              ],
              contactPoint: {
                "@type": "ContactPoint",
                telephone: "+966501234567",
                contactType: "customer service",
                availableLanguage: ["Arabic", "English"],
              },
              areaServed: {
                "@type": "Country",
                name: "Saudi Arabia",
              },
              serviceType: "Gaming Cards and Digital Services",
            }),
          }}
        />

        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "WebSite",
              name: "نيران كارد",
              url: "https://nerancard.com",
              potentialAction: {
                "@type": "SearchAction",
                target: "https://nerancard.com/search?q={search_term_string}",
                "query-input": "required name=search_term_string",
              },
            }),
          }}
        />
      </head>
      <body className={cairo.className}>
        <UserProvider>
          <Navbar />
          <main>{children}</main>
        </UserProvider>
      </body>
    </html>
  )
}
