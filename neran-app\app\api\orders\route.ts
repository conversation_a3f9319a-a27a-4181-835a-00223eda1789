/**
 * Orders API Route
 * Handles fetching user orders
 */

import { NextRequest, NextResponse } from 'next/server'
import { OrderService } from '../../../lib/services/orders'
import { TransactionService } from '../../../lib/services/transactions'
import { SupabaseAdminService } from '../../../lib/services/supabase-admin'
import { createSupabaseServerClient } from '../../../lib/supabase-server'
import { TelegramBotService } from '../../../lib/services/telegram-bot'
import { getEffectivePrice, getEffectivePriceByRole } from '../../../lib/utils/offers'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')
    const limit = parseInt(searchParams.get('limit') || '50')

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      )
    }

    console.log(`📦 Fetching orders for user: ${userId}`)

    const orders = await OrderService.getUserOrders(userId, limit)

    console.log(`✅ Fetched ${orders.length} orders`)

    return NextResponse.json(orders)
  } catch (error: any) {
    console.error('❌ Orders API error:', error)
    
    // Return empty array instead of error for graceful handling
    return NextResponse.json([])
  }
}

export async function POST(request: NextRequest) {
  try {
    console.log('📝 Creating new order from home page')

    // 🔒 SECURITY: Authenticate user first
    const supabase = await createSupabaseServerClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      console.log('❌ Unauthorized order attempt')
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const orderData = await request.json()

    // 🔒 SECURITY: Validate user ID matches authenticated user
    if (orderData.userId !== user.id) {
      console.log('❌ User ID mismatch - potential security breach attempt')
      return NextResponse.json(
        { error: 'Invalid user ID' },
        { status: 403 }
      )
    }

    // Validate required fields
    if (!orderData.userId || !orderData.productId || !orderData.gameId) {
      return NextResponse.json(
        { error: 'Missing required fields: userId, productId, gameId' },
        { status: 400 }
      )
    }

    // 🔒 CRITICAL SECURITY: Validate price against database (prevent price manipulation)
    const product = await SupabaseAdminService.getProductById(orderData.productId)

    if (!product) {
      return NextResponse.json(
        { error: 'Product not found' },
        { status: 404 }
      )
    }

    if (!product.isActive) {
      return NextResponse.json(
        { error: 'Product is not available' },
        { status: 400 }
      )
    }

    // 🔒 SECURITY: Get user profile and validate status & balance
    const userProfile = await SupabaseAdminService.getUserById(user.id)
    if (!userProfile) {
      return NextResponse.json(
        { error: 'User profile not found' },
        { status: 404 }
      )
    }

    if (userProfile.status !== 'active') {
      return NextResponse.json(
        { error: 'Account not active. Please contact support.' },
        { status: 403 }
      )
    }

    console.log('📦 Order data received:', orderData)

    // 🔒 SECURITY: Calculate effective price (considering role and offers/discounts) on server-side
    const serverPrice = getEffectivePriceByRole(product, userProfile.role)
    
    console.log(`🔒 SECURITY: User role: ${userProfile.role}, Product base price: ${product.price}, Distributor price: ${product.distributorPrice}, Effective price: ${serverPrice}`)
    console.log(`🔒 SECURITY: Using server effective price ${serverPrice} instead of client price ${orderData.price}`)

    // 🔒 SECURITY: Check balance after calculating role-based price
    if (userProfile.balance < serverPrice) {
      return NextResponse.json(
        { error: 'Insufficient balance' },
        { status: 400 }
      )
    }

    // 🔒 SECURITY: Get system settings for validation
    const settings = await SupabaseAdminService.getSystemSettings()

    // 🔒 SECURITY: Create order with authenticated user context (respects RLS)
    // Use server client with user context instead of bypassing RLS
    const { data: orderData_db, error: orderError } = await supabase
      .from('orders')
      .insert({
        user_id: orderData.userId,
        product_id: orderData.productId,
        product_name: product.name || orderData.productName || 'Unknown Product', // Use product name from database with fallback
        game_id: orderData.gameId,
        amount: orderData.amount || orderData.productName,
        price: serverPrice,
        status: 'pending',
        notes: orderData.notes || null
      })
      .select()
      .single()

    if (orderError) {
      console.error('❌ Create order error:', orderError)
      return NextResponse.json(
        { error: 'Failed to create order' },
        { status: 500 }
      )
    }

    // Map to app format
    const newOrder = {
      id: orderData_db.id,
      userId: orderData_db.user_id,
      gameId: orderData_db.game_id,
      product: orderData_db.product_name, // Use product name from database
      productId: orderData_db.product_id,
      amount: orderData_db.amount,
      price: serverPrice,
      status: orderData_db.status,
      createdAt: orderData_db.created_at,
      updatedAt: orderData_db.updated_at,
      completedAt: orderData_db.completed_at,
      notes: orderData_db.notes,
      adminNotes: orderData_db.admin_notes
    }

    console.log('✅ Order created successfully:', newOrder.id)

    // ✅ REAL-TIME: Create purchase transaction to update user balance
    // This transaction will be visible in wallet page after cache refresh
    const transaction = await TransactionService.createOrderPurchaseTransaction(
      orderData.userId,
      newOrder.id,
      serverPrice,
      product.name // Use product name from database
    )

    console.log('✅ Purchase transaction created:', transaction.id)

    // 🔔 TELEGRAM NOTIFICATION: Send order notification to admin
    try {
      await TelegramBotService.sendOrderNotification({
        id: orderData_db.id,
        user_name: userProfile.name,
        user_email: userProfile.email,
        product_name: product.name,
        game_id: orderData_db.game_id,
        amount: orderData_db.amount,
        price: serverPrice,
        status: orderData_db.status,
        created_at: orderData_db.created_at
      })
    } catch (telegramError) {
      // Don't fail the order if Telegram notification fails
      console.warn('⚠️ Telegram notification failed (non-critical):', telegramError)
    }

    return NextResponse.json({
      success: true,
      order: newOrder,
      transaction: transaction,
      message: 'Order created successfully'
    }, {
      status: 201,
      headers: {
        'Content-Type': 'application/json',
      },
    })
  } catch (error: any) {
    console.error('❌ Order creation error:', error)
    return NextResponse.json(
      { error: error.message || 'Failed to create order' },
      { status: 500 }
    )
  }
}
