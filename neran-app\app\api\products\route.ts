import { NextResponse } from 'next/server'
import { SupabaseAdminService } from '../../../lib/services/supabase-admin'
import { serializeActiveProducts } from '../../../lib/utils/product-serialization'

/**
 * Products API Route
 * Returns your REAL product data from Firebase database
 * NO CACHING - Always fresh data
 */
export async function GET() {
  try {
    // Fetch your actual products from database using SupabaseAdminService
    const allProducts = await SupabaseAdminService.getAllProducts()

    // Use shared serialization logic to ensure consistency
    const serializedProducts = serializeActiveProducts(allProducts)

    // NO CACHING - Always return fresh data
    return NextResponse.json(serializedProducts, {
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
        'Content-Type': 'application/json',
      },
    })
  } catch (error) {
    console.error('❌ Products API error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch products', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}
