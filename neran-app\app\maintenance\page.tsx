'use client'

import { Wrench, Clock, Mail, MessageCircle } from 'lucide-react'
import { useState, useEffect } from 'react'

export default function MaintenancePage() {
  const [whatsAppNumber, setWhatsAppNumber] = useState<string>("+966501234567")

  // Fetch WhatsApp number from admin settings
  useEffect(() => {
    const fetchWhatsAppNumber = async () => {
      try {
        const response = await fetch('/api/settings')
        if (response.ok) {
          const settings = await response.json()
          if (settings && settings.supportWhatsApp) {
            setWhatsAppNumber(settings.supportWhatsApp)
          }
        }
      } catch (error) {
        console.warn('Could not fetch WhatsApp number from settings, using default:', error)
      }
    }

    fetchWhatsAppNumber()
  }, [])
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black flex items-center justify-center p-4">
      <div className="max-w-md w-full text-center">
        {/* Logo/Icon */}
        <div className="mb-8">
          <div className="w-24 h-24 mx-auto bg-red-600 rounded-full flex items-center justify-center mb-4">
            <Wrench className="w-12 h-12 text-white" />
          </div>
          <h1 className="text-3xl font-bold text-white mb-2">نيران كارد</h1>
        </div>

        {/* Maintenance Message */}
        <div className="bg-gray-800 border border-gray-600 rounded-xl p-6 mb-6">
          <div className="flex items-center justify-center mb-4">
            <Clock className="w-8 h-8 text-yellow-400 mr-3" />
            <h2 className="text-xl font-bold text-white">الموقع تحت الصيانة</h2>
          </div>
          
          <p className="text-gray-300 mb-4">
            نعتذر عن الإزعاج. نحن نعمل حالياً على تحسين الموقع وإضافة ميزات جديدة لتحسين تجربتك.
          </p>
          
          <p className="text-gray-400 text-sm">
            سيعود الموقع للعمل قريباً. شكراً لصبركم.
          </p>
        </div>

        {/* Contact Information */}
        <div className="bg-gray-800 border border-gray-600 rounded-xl p-4 mb-6">
          <h3 className="text-lg font-semibold text-white mb-3">تحتاج مساعدة؟</h3>
          
          <div className="space-y-3">
            <a 
              href={`https://wa.me/${whatsAppNumber.replace(/\+/g, '')}`}
              target="_blank" 
              rel="noopener noreferrer"
              className="flex items-center justify-center bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors"
            >
              <MessageCircle className="w-4 h-4 mr-2" />
              واتساب الدعم
            </a>
            
            <a 
              href="mailto:<EMAIL>"
              className="flex items-center justify-center bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
            >
              <Mail className="w-4 h-4 mr-2" />
              البريد الإلكتروني
            </a>
          </div>
        </div>

        {/* Status Updates */}
        <div className="text-center">
          <p className="text-gray-400 text-sm mb-2">للحصول على آخر التحديثات:</p>
          <div className="flex justify-center space-x-4 space-x-reverse">
            <span className="text-gray-500 text-xs">تابعنا على وسائل التواصل الاجتماعي</span>
          </div>
        </div>

        {/* Auto-refresh notice */}
        <div className="mt-8 p-3 bg-gray-900 border border-gray-700 rounded-lg">
          <p className="text-gray-400 text-xs">
            ⏱️ سيتم تحديث هذه الصفحة تلقائياً كل دقيقة للتحقق من حالة الموقع
          </p>
        </div>
      </div>

      {/* Auto-refresh script */}
      <script
        dangerouslySetInnerHTML={{
          __html: `
            // Auto-refresh every minute to check if maintenance is over
            setTimeout(function() {
              window.location.reload();
            }, 60000);
          `,
        }}
      />
    </div>
  )
}


