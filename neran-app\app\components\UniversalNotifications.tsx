'use client'

import { useEffect, useState } from 'react'
import { X, RefreshCw } from 'lucide-react'
import { NotificationService, NotificationData, NotificationAction } from '../../lib/services/notification-service'

export default function UniversalNotifications() {
  const [notifications, setNotifications] = useState<NotificationData[]>([])

  useEffect(() => {
    // Load persistent notifications on mount
    NotificationService.loadPersistentNotifications()

    // Subscribe to notification changes
    const unsubscribe = NotificationService.subscribe(setNotifications)

    return unsubscribe
  }, [])

  const handleActionClick = (action: NotificationAction, notificationId: string) => {
    if (action.onClick) {
      action.onClick()
    }
    // Auto-dismiss after action unless it's a persistent notification
    const notification = notifications.find(n => n.id === notificationId)
    if (notification && !notification.persistent) {
      NotificationService.dismiss(notificationId)
    }
  }

  const getPositionClasses = (position: string) => {
    switch (position) {
      case 'top-left':
        return 'top-4 left-4 md:left-4 left-2'
      case 'top-center':
        return 'top-4 left-1/2 transform -translate-x-1/2'
      case 'top-right':
        return 'top-4 right-4 md:right-4 right-2'
      case 'bottom-left':
        return 'bottom-4 left-4 md:left-4 left-2'
      case 'bottom-center':
        return 'bottom-4 left-1/2 transform -translate-x-1/2'
      case 'bottom-right':
        return 'bottom-4 right-4 md:right-4 right-2'
      case 'center':
        return 'top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 px-4'
      default:
        return 'top-4 right-4 md:right-4 right-2'
    }
  }

  const getTypeStyles = (notification: NotificationData) => {
    if (notification.gradient) {
      return `bg-gradient-to-r ${notification.gradient}`
    }

    switch (notification.type) {
      case 'success':
        return 'bg-gradient-to-r from-green-500 to-green-600'
      case 'error':
        return 'bg-gradient-to-r from-red-500 to-red-600'
      case 'warning':
        return 'bg-gradient-to-r from-yellow-500 to-yellow-600'
      case 'info':
        return 'bg-gradient-to-r from-blue-500 to-blue-600'
      case 'promotion':
        return 'bg-gradient-to-r from-purple-500 to-purple-600'
      default:
        return 'bg-gradient-to-r from-gray-500 to-gray-600'
    }
  }

  const getActionButtonStyles = (variant?: string) => {
    switch (variant) {
      case 'primary':
        return 'bg-white text-gray-800 hover:bg-gray-100'
      case 'danger':
        return 'bg-red-500 text-white hover:bg-red-600'
      case 'secondary':
      default:
        return 'bg-transparent text-white border border-white/30 hover:bg-white/10'
    }
  }

  // Group notifications by position
  const notificationsByPosition = notifications.reduce((acc, notification) => {
    const position = notification.position || 'top-right'
    if (!acc[position]) {
      acc[position] = []
    }
    acc[position].push(notification)
    return acc
  }, {} as Record<string, NotificationData[]>)

  return (
    <>
      {Object.entries(notificationsByPosition).map(([position, positionNotifications]) => (
        <div
          key={position}
          className={`fixed z-50 w-full max-w-sm md:max-w-sm sm:max-w-xs ${getPositionClasses(position)}`}
        >
          <div className="space-y-3 px-2 md:px-0">
            {positionNotifications.map((notification) => (
              <div
                key={notification.id}
                className={`
                  ${getTypeStyles(notification)}
                  border border-white/20 rounded-xl p-4 shadow-2xl backdrop-blur-sm
                  animate-in slide-in-from-right-4 fade-in duration-500 ease-out
                  hover:scale-105 transition-all duration-200
                  ${notification.customStyles || ''}
                `}
              >
                <div className="flex items-start gap-3">
                  {/* Icon */}
                  {notification.icon && (
                    <div className="w-10 h-10 md:w-8 md:h-8 bg-white/20 rounded-full flex items-center justify-center flex-shrink-0 animate-pulse">
                      <span className="text-xl md:text-lg">{notification.icon}</span>
                    </div>
                  )}

                  {/* Content */}
                  <div className="flex-1 min-w-0">
                    <h3 className="text-white font-bold mb-2 text-sm md:text-sm leading-tight">
                      {notification.title}
                    </h3>
                    <p className="text-white/95 text-sm md:text-xs leading-relaxed whitespace-pre-line">
                      {notification.message}
                    </p>
                    
                    {/* Actions */}
                    {notification.actions && notification.actions.length > 0 && (
                      <div className="flex flex-wrap gap-2 mt-4">
                        {notification.actions.map((action, index) => (
                          <button
                            key={index}
                            onClick={() => handleActionClick(action, notification.id)}
                            disabled={action.loading}
                            className={`
                              px-4 py-2 md:px-3 md:py-1.5 rounded-lg text-sm font-bold
                              disabled:opacity-50 disabled:cursor-not-allowed
                              flex items-center gap-2 transition-all duration-200
                              hover:scale-105 active:scale-95 min-h-[40px] md:min-h-auto
                              ${getActionButtonStyles(action.variant)}
                            `}
                          >
                            {action.loading && (
                              <RefreshCw className="w-4 h-4 md:w-3 md:h-3 animate-spin" />
                            )}
                            {action.label}
                          </button>
                        ))}
                      </div>
                    )}
                  </div>
                  
                  {/* Dismiss button */}
                  {notification.dismissible && (
                    <button
                      onClick={() => NotificationService.dismiss(notification.id)}
                      className="text-white/70 hover:text-white p-2 md:p-1 transition-all duration-200
                                hover:bg-white/10 rounded-full min-w-[40px] md:min-w-auto h-10 md:h-auto
                                flex items-center justify-center"
                    >
                      <X className="w-5 h-5 md:w-4 md:h-4" />
                    </button>
                  )}
                </div>

                {/* Priority indicator */}
                {notification.priority === 'urgent' && (
                  <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse" />
                )}
              </div>
            ))}
          </div>
        </div>
      ))}
    </>
  )
}
