"use client"

interface Offer {
  id: string
  price: number
  title: string
  type: "gems" | "membership" | "pass"
}

const offers: Offer[] = [
  { id: "1", price: 2.25, title: "💎 110 جوهرة", type: "gems" },
  { id: "2", price: 6.75, title: "💎 341 جوهرة", type: "gems" },
  { id: "3", price: 11.25, title: "💎 571 جوهرة", type: "gems" },
  { id: "4", price: 22.5, title: "💎 1166 جوهرة", type: "gems" },
  { id: "5", price: 45.0, title: "💎 2398 جوهرة", type: "gems" },
  { id: "6", price: 4.6, title: "عضوية أسبوعية", type: "membership" },
  { id: "7", price: 16.0, title: "عضوية شهرية", type: "membership" },
  { id: "8", price: 6.9, title: "بوياه باس", type: "pass" },
]

interface OfferCardsProps {
  selectedOffer: Offer | null
  setSelectedOffer: (offer: Offer | null) => void
}

export default function OfferCards({ selectedOffer, setSelectedOffer }: OfferCardsProps) {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
      {offers.map((offer) => (
        <button
          key={offer.id}
          onClick={() => setSelectedOffer(offer)}
          className={`p-4 rounded-lg border-2 transition-all duration-200 text-right hover:scale-105 ${
            selectedOffer?.id === offer.id
              ? "border-red-500 shadow-lg shadow-red-500/20"
              : "border-gray-600 hover:border-red-800"
          }`}
          style={{
            backgroundColor: selectedOffer?.id === offer.id ? "rgba(239, 68, 68, 0.1)" : "#2a2a2a",
          }}
        >
          <div className="space-y-2">
            <div className="text-lg font-bold text-red-400">${offer.price}</div>
            <div className="text-white font-semibold">{offer.title}</div>
          </div>
        </button>
      ))}
    </div>
  )
}
