/**
 * Supabase Authentication Service
 * Replaces Firebase Auth with Supa<PERSON> Auth
 */

import { createSupabaseBrowserClient } from '../supabase'
import { User } from '../../types'

// Rate limiting protection
class RateLimiter {
  private static requests: Map<string, number[]> = new Map()
  private static readonly MAX_REQUESTS = 3 // Reduced to 3 requests
  private static readonly WINDOW_MS = 60000 // 1 minute

  static canMakeRequest(key: string): boolean {
    const now = Date.now()
    const requests = this.requests.get(key) || []
    
    // Remove old requests outside the window
    const validRequests = requests.filter(time => now - time < this.WINDOW_MS)
    
    if (validRequests.length >= this.MAX_REQUESTS) {
      console.warn(`⚠️ Rate limit reached for ${key}. ${validRequests.length}/${this.MAX_REQUESTS} requests in window`)
      return false
    }
    
    validRequests.push(now)
    this.requests.set(key, validRequests)
    return true
  }

  static getRemainingRequests(key: string): number {
    const now = Date.now()
    const requests = this.requests.get(key) || []
    const validRequests = requests.filter(time => now - time < this.WINDOW_MS)
    return Math.max(0, this.MAX_REQUESTS - validRequests.length)
  }
}

export class SupabaseAuthService {
  private static _supabase: ReturnType<typeof createSupabaseBrowserClient> | null = null
  
  // Singleton pattern for client
  static get supabase() {
    if (!this._supabase) {
      this._supabase = createSupabaseBrowserClient()
      if (process.env.NODE_ENV === 'development') {
        console.log('🔧 SupabaseAuthService: Created singleton client instance')
      }
    }
    return this._supabase
  }

  /**
   * Sign up new user
   */
  static async signUp(email: string, password: string, name: string): Promise<User> {
    if (!RateLimiter.canMakeRequest(`signup-${email}`)) {
      throw new Error('تم تجاوز عدد المحاولات المسموح، يرجى المحاولة بعد قليل')
    }

    try {
      console.log('🔐 Starting Supabase signup process for:', email)

      // Use Supabase client directly to ensure session cookies are set
      const { data: authData, error: authError } = await this.supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            name: name
          }
        }
      })

      if (authError) {
        console.error('❌ Supabase Auth signup error:', authError)
        throw new Error(authError.message)
      }

      if (!authData.user) {
        throw new Error('No user data returned from signup')
      }

      // Check if user needs email confirmation
      if (!authData.session) {
        throw new Error('يرجى تأكيد بريدك الإلكتروني قبل تسجيل الدخول')
      }

      // Get user profile from our user_profiles table (should be created by trigger)
      const { data: userData, error: dbError } = await this.supabase
        .from('user_profiles')
        .select('*')
        .eq('id', authData.user.id)
        .single()

      if (dbError) {
        console.error('❌ Database user fetch error:', dbError)
        throw new Error('Failed to fetch user profile')
      }

      console.log('✅ Signup successful')
      return this.mapSupabaseUserToAppUser(userData)

    } catch (error: any) {
      console.error('❌ Signup failed:', error)
      throw new Error(error.message || 'حدث خطأ أثناء إنشاء الحساب')
    }
  }

  /**
   * Sign in existing user
   */
  static async signIn(email: string, password: string): Promise<User> {
    if (!RateLimiter.canMakeRequest(`signin-${email}`)) {
      throw new Error('تم تجاوز عدد المحاولات المسموح، يرجى المحاولة بعد قليل')
    }

    try {
      console.log('🔑 Starting Supabase signin process for:', email)

      // Use Supabase client directly to ensure session cookies are set
      const { data: authData, error: authError } = await this.supabase.auth.signInWithPassword({
        email,
        password
      })

      if (authError) {
        console.error('❌ Supabase Auth signin error:', authError)
        throw new Error(authError.message)
      }

      if (!authData.user) {
        throw new Error('No user data returned from signin')
      }

      // Get user profile from our user_profiles table
      const { data: userData, error: dbError } = await this.supabase
        .from('user_profiles')
        .select('*')
        .eq('id', authData.user.id)
        .single()

      if (dbError) {
        console.error('❌ Database user fetch error:', dbError)
        throw new Error('Failed to fetch user profile')
      }

      console.log('✅ Signin successful')
      return this.mapSupabaseUserToAppUser(userData)

    } catch (error: any) {
      console.error('❌ Signin failed:', error)
      throw new Error(error.message || 'حدث خطأ أثناء تسجيل الدخول')
    }
  }

  /**
   * Sign out user
   */
  static async signOut(): Promise<void> {
    try {
      console.log('🚪 Signing out user')
      
      const { error } = await this.supabase.auth.signOut()
      
      if (error) {
        console.error('❌ Signout error:', error)
        throw new Error(error.message)
      }

      console.log('✅ Signout successful')
    } catch (error: any) {
      console.error('❌ Signout failed:', error)
      throw new Error(error.message || 'حدث خطأ أثناء تسجيل الخروج')
    }
  }

  /**
   * Get current user with rate limiting
   */
  static async getCurrentUser(): Promise<User | null> {
    if (!RateLimiter.canMakeRequest('getCurrentUser')) {
      console.warn('⚠️ Rate limit reached for getCurrentUser, returning cached data')
      // Try to return cached data from localStorage
      try {
        const cached = localStorage.getItem('neran_user')
        if (cached) {
          const userData = JSON.parse(cached)
          const timestamp = localStorage.getItem('neran_user_timestamp')
          if (timestamp && Date.now() - parseInt(timestamp) < 600000) { // Increased to 10 minutes
            if (process.env.NODE_ENV === 'development') {
              console.log('📋 Returning cached user data due to rate limit')
            }
            return userData
          }
        }
      } catch (e) {
        console.warn('Failed to get cached user data')
      }
      // If no valid cache, still return null instead of throwing
      return null
    }

    try {
      const { data: { user: authUser }, error: authError } = await this.supabase.auth.getUser()

      if (authError) {
        console.error('❌ Get current user error:', authError)
        return null
      }

      if (!authUser) {
        return null
      }

      // Get user profile from our user_profiles table
      const { data: userData, error: dbError } = await this.supabase
        .from('user_profiles')
        .select('*')
        .eq('id', authUser.id)
        .single()

      if (dbError) {
        console.error('❌ Database user fetch error:', dbError)
        return null
      }

      return this.mapSupabaseUserToAppUser(userData)

    } catch (error: any) {
      console.error('❌ Get current user failed:', error)
      return null
    }
  }

  /**
   * Update user profile via API endpoint
   */
  static async updateUserProfile(userId: string, updates: Partial<User>): Promise<User> {
    try {
      console.log('🚀 NEW API METHOD: Updating user profile via API endpoint for user:', userId)
      console.log('🔧 NEW API METHOD: Update data:', updates)

      const requestBody = {
        name: updates.name,
      }
      console.log('📤 NEW API METHOD: Sending request body:', requestBody)

      const response = await fetch('/api/user/profile', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      })

      console.log('📊 NEW API METHOD: Response status:', response.status, response.statusText)

      if (!response.ok) {
        const errorText = await response.text()
        console.error('❌ NEW API METHOD: Response error:', errorText)
        try {
          const errorData = JSON.parse(errorText)
          throw new Error(errorData.error || 'Failed to update profile')
        } catch {
          throw new Error('Network error: ' + response.status)
        }
      }

      const result = await response.json()
      console.log('📊 NEW API METHOD: Response result:', result)
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to update profile')
      }

      console.log('✅ NEW API METHOD: User profile updated successfully via API')
      return result.user

    } catch (error: any) {
      console.error('❌ NEW API METHOD: Update user profile failed:', error)
      throw new Error(error.message || 'حدث خطأ أثناء تحديث الملف الشخصي')
    }
  }

  /**
   * Listen to auth state changes
   */
  static onAuthStateChange(callback: (user: User | null) => void) {
    return this.supabase.auth.onAuthStateChange(async (event: any, session: any) => {
      if (process.env.NODE_ENV === 'development') {
        console.log('🔄 Auth state changed:', event, 'Session exists:', !!session)
      }

      try {
        if (session?.user) {
          if (process.env.NODE_ENV === 'development') {
            console.log('🔍 Fetching user profile for ID:', session.user.id)
          }
          
          if (process.env.NODE_ENV === 'development') {
            console.warn('🔄 Skipping direct database query, trying server method due to known RLS issues')
          }
          
          // Skip direct database query and go straight to server endpoint
          try {
            // Add timeout to prevent hanging requests
            const controller = new AbortController()
            const timeoutId = setTimeout(() => controller.abort(), 8000) // 8 second timeout
            
            const response = await fetch('/api/user/profile', {
              method: 'GET',
              headers: {
                'Content-Type': 'application/json'
              },
              credentials: 'include', // Include cookies for session
              signal: controller.signal
            })
            
            clearTimeout(timeoutId)
            
            if (process.env.NODE_ENV === 'development') {
              console.log('📊 Server endpoint response:', response.status, response.statusText)
            }
            
            if (response.ok) {
              const result = await response.json()
              if (process.env.NODE_ENV === 'development') {
                console.log('📊 Server endpoint result:', result)
              }
              
              if (result.success && result.user) {
                if (process.env.NODE_ENV === 'development') {
                  console.log('✅ User profile fetched via server endpoint')
                }
                callback(result.user)
                return
              }
            } else if (response.status === 401) {
              // Handle different types of 401 errors
              let errorInfo = null
              try {
                errorInfo = await response.json()
              } catch (e) {
                // Ignore JSON parse errors
              }
              
              if (process.env.NODE_ENV === 'development') {
                console.log('🔐 Server endpoint returned 401:', errorInfo?.code || 'UNKNOWN')
              }
              
              // For session expired errors, try cached data longer
              if (errorInfo?.code === 'SESSION_EXPIRED') {
                try {
                  const cached = localStorage.getItem('neran_user')
                  if (cached) {
                    const userData = JSON.parse(cached)
                    const timestamp = localStorage.getItem('neran_user_timestamp')
                    if (timestamp && Date.now() - parseInt(timestamp) < 900000) { // 15 minutes for session expired
                      if (process.env.NODE_ENV === 'development') {
                        console.log('📋 Using cached data for session expired (15min grace)')
                      }
                      callback(userData)
                      return
                    }
                  }
                } catch (e) {
                  console.warn('Failed to get cached user data after session expired')
                }
              } else {
                // For other 401 errors, try shorter cache window
                try {
                  const cached = localStorage.getItem('neran_user')
                  if (cached) {
                    const userData = JSON.parse(cached)
                    const timestamp = localStorage.getItem('neran_user_timestamp')
                    if (timestamp && Date.now() - parseInt(timestamp) < 300000) { // 5 minutes for other errors
                      if (process.env.NODE_ENV === 'development') {
                        console.log('📋 Using cached data for auth error (5min grace)')
                      }
                      callback(userData)
                      return
                    }
                  }
                } catch (e) {
                  console.warn('Failed to get cached user data after 401')
                }
              }
              
              // Only sign out if we can't use cached data
              if (process.env.NODE_ENV === 'development') {
                console.log('🚪 No valid cached data, proceeding with sign out')
              }
              callback(null)
              return
            } else if (response.status === 500) {
              // Server error, try to get cached data
              if (process.env.NODE_ENV === 'development') {
                console.warn('⚠️ Server endpoint returned 500, trying cached data')
              }
              // Try cached data as fallback
              try {
                const cached = localStorage.getItem('neran_user')
                if (cached) {
                  const userData = JSON.parse(cached)
                  const timestamp = localStorage.getItem('neran_user_timestamp')
                  if (timestamp && Date.now() - parseInt(timestamp) < 300000) { // 5 minutes
                    if (process.env.NODE_ENV === 'development') {
                      console.log('📋 Using cached data due to server error')
                    }
                    callback(userData)
                    return
                  }
                }
              } catch (e) {
                console.warn('Failed to get cached user data after server error')
              }
            }
          } catch (serverError: any) {
            if (process.env.NODE_ENV === 'development') {
              if (serverError.name === 'AbortError') {
                console.warn('⏰ Server endpoint request timed out')
              } else {
                console.warn('❌ Server endpoint failed:', serverError)
              }
            }
          }
          
          if (process.env.NODE_ENV === 'development') {
            console.warn('❌ All methods failed, calling callback with null')
          }
          callback(null)
        } else {
          if (process.env.NODE_ENV === 'development') {
            console.log('🚪 No session user, calling callback with null')
          }
          callback(null)
        }
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          console.error('❌ Auth state change error:', error)
        }
        callback(null)
      }
    })
  }

  /**
   * Map Supabase user data to our app User type
   */
  private static mapSupabaseUserToAppUser(supabaseUser: any): User {
    return {
      id: supabaseUser.id,
      email: supabaseUser.email,
      name: supabaseUser.name,
      role: supabaseUser.role,
      status: supabaseUser.status,
      balance: supabaseUser.balance,
      createdAt: supabaseUser.created_at,
      updatedAt: supabaseUser.updated_at,
    }
  }

  /**
   * Reset password
   */
  static async resetPassword(email: string): Promise<void> {
    try {
      console.log('🔄 Sending password reset email to:', email)

      const { error } = await this.supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`
      })

      if (error) {
        console.error('❌ Password reset error:', error)
        throw new Error(error.message)
      }

      console.log('✅ Password reset email sent')
    } catch (error: any) {
      console.error('❌ Password reset failed:', error)
      throw new Error(error.message || 'حدث خطأ أثناء إرسال رابط إعادة تعيين كلمة المرور')
    }
  }

  /**
   * Update password with current password verification
   */
  static async updatePassword(currentPassword: string, newPassword: string): Promise<void> {
    try {
      console.log('🔄 Updating password with verification')

      // First, verify the current password by attempting to sign in
      const { data: { user } } = await this.supabase.auth.getUser()
      if (!user) {
        throw new Error('المستخدم غير مسجل الدخول')
      }

      // Verify current password by attempting to sign in
      const { error: verifyError } = await this.supabase.auth.signInWithPassword({
        email: user.email!,
        password: currentPassword
      })

      if (verifyError) {
        console.error('❌ Current password verification failed:', verifyError)
        throw new Error('كلمة المرور الحالية غير صحيحة')
      }

      console.log('✅ Current password verified, updating to new password')

      // Now update the password
      const { error } = await this.supabase.auth.updateUser({
        password: newPassword
      })

      if (error) {
        console.error('❌ Password update error:', error)
        throw new Error(error.message)
      }

      console.log('✅ Password updated successfully')
    } catch (error: any) {
      console.error('❌ Password update failed:', error)
      throw new Error(error.message || 'حدث خطأ أثناء تحديث كلمة المرور')
    }
  }
}
