import { Product } from '../../types'
import { SSRProduct } from '../ssr-helpers'

/**
 * Serialize a Supabase product to a format safe for JSON transmission
 * This ensures consistent handling of PostgreSQL timestamps and offer data
 */
export function serializeProduct(product: Product): SSRProduct {
  return {
    id: product.id,
    name: product.name,
    category: product.category,
    price: product.price,
    distributorPrice: product.distributorPrice,
    description: product.description,
    isActive: product.isActive,
    createdAt: typeof product.createdAt === 'string' ? product.createdAt : new Date().toISOString(),
    updatedAt: typeof product.updatedAt === 'string' ? product.updatedAt : new Date().toISOString(),
    imageUrl: product.imageUrl || undefined,
    gameType: product.gameType || undefined,
    sortOrder: product.sortOrder || 0,
    // Offer fields - ensure they're properly serialized
    hasOffer: Boolean(product.hasOffer),
    // Enhanced UI fields
    popular: <PERSON>olean(product.popular),
    specialOffer: Boolean(product.specialOffer || product.hasOffer),
    originalPrice: product.originalPrice || undefined,
    discountPercentage: product.discountPercentage || undefined,
    offerStartDate: typeof product.offerStartDate === 'string' ? product.offerStartDate : undefined,
    offerEndDate: typeof product.offerEndDate === 'string' ? product.offerEndDate : undefined,
  }
}

/**
 * Serialize multiple products
 */
export function serializeProducts(products: Product[]): SSRProduct[] {
  return products.map(serializeProduct)
}

/**
 * Filter and serialize active products
 */
export function serializeActiveProducts(products: Product[]): SSRProduct[] {
  const activeProducts = products.filter(product => product.isActive)
  return serializeProducts(activeProducts)
}


