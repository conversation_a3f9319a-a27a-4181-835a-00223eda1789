import { Suspense } from "react"
import { <PERSON>ada<PERSON> } from "next"
import { getInitialPageData } from "../lib/ssr-helpers"
import HomePageClient from "./components/HomePageClient"
import { ProductCardSkeleton } from "./components/LoadingSpinner"

// Prevent caching of the home page to ensure fresh product data
export const metadata: Metadata = {
  title: "الصفحة الرئيسية - نيران كارد",
  description: "أفضل موقع لشحن بطاقات الألعاب والتطبيقات",
  other: {
    'Cache-Control': 'no-cache, no-store, must-revalidate',
    'Pragma': 'no-cache',
    'Expires': '0'
  }
}

// Force dynamic rendering to prevent static caching
export const dynamic = 'force-dynamic'
export const revalidate = 0

/**
 * SSR-Optimized Home Page
 * Data is fetched on the server for better performance and SEO
 */
export default async function HomePage() {
  // Fetch initial data on the server
  const initialData = await getInitialPageData()

  return (
    <div className="min-h-screen w-full px-4 sm:px-6 py-8">
      {/* Hero Section */}
      <div className="text-center mb-12">
        <h1 className="text-4xl md:text-5xl font-bold text-white mb-4 bg-gradient-to-r from-red-400 to-red-600 bg-clip-text text-transparent">
          {initialData.settings.siteName}
        </h1>
        <p className="text-xl text-gray-300 mb-8">
          أفضل موقع لشحن بطاقات الألعاب والتطبيقات
        </p>
      </div>

      {/* Client-side interactive components */}
      <Suspense fallback={
        <div className="grid grid-cols-2 gap-4 sm:gap-6">
          {[...Array(6)].map((_, i) => (
            <ProductCardSkeleton key={i} />
          ))}
        </div>
      }>
        <HomePageClient
          initialProducts={initialData.products}
          initialSettings={initialData.settings}
        />
      </Suspense>
    </div>
  )
}
